"use server";

import { AppointmentType } from "@prisma/client";
import { generateVerificationToken } from "auth/lib/tokens";
import { sendEmail } from "mail";
import { EvolutionService } from "../integrations/evolution/evolution.service";
import { formatPhoneForWhatsApp } from "../../../lib/utils/format-phone";

// Types
interface AppointmentData {
  id: string;
  scheduledAt: Date;
  type?: AppointmentType;
  url?: string;
  isOnDuty?: boolean;
}

interface PatientData {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  isNewUser?: boolean;
  tempPassword?: string;
}

interface DoctorData {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
}

interface NotificationOptions {
  sendEmail?: boolean;
  sendWhatsApp?: boolean;
  useDirectLinks?: boolean; // If true, generate direct login links with tokens
}

interface NotificationResult {
  success: boolean;
  email?: {
    sent: boolean;
    error?: string;
  };
  whatsApp?: {
    sent: boolean;
    error?: string;
    messageCount?: number;
  };
  error?: string;
}

/**
 * Unified notification service that sends both email and WhatsApp notifications
 * for appointments to patients and doctors
 */
export async function sendAppointmentNotifications(
  appointment: AppointmentData,
  patient: PatientData,
  doctor: DoctorData,
  options: NotificationOptions = { sendEmail: true, sendWhatsApp: true, useDirectLinks: true }
): Promise<NotificationResult | undefined> {
  console.log("[NOTIFICATIONS] Iniciando envio de notificações:", {
    appointmentId: appointment.id,
    patientId: patient.id,
    doctorId: doctor.id,
    options,
    date: new Date().toISOString(),
    environment: process.env.VERCEL_ENV || "local" // Log the environment
  });

  try {
    // Generate base URLs - Fix for Vercel environment
    let baseUrl = process.env.NEXT_PUBLIC_SITE_URL;

    // Ensure we have a valid base URL
    if (!baseUrl || baseUrl.trim() === "") {
      console.warn("[NOTIFICATIONS_URL_WARNING] NEXT_PUBLIC_SITE_URL não definido, usando fallback");
      baseUrl = "https://zapvida.com";
    }

    console.log("[NOTIFICATIONS_URL] Usando base URL:", baseUrl);
    
    // Determine if this is a plantão appointment
    const isPlantao = appointment.isOnDuty === true;
    
    // Use appropriate URL based on appointment type
    const appointmentUrl = appointment.url || 
      (isPlantao ? `${baseUrl}/patient/zapchat/${appointment.id}` : `${baseUrl}/chats/${appointment.id}`);
    
    console.log("[NOTIFICATIONS_URL] URL gerada:", { 
      appointmentUrl, 
      isPlantao, 
      appointmentType: appointment.type 
    });

    // Result object
    const result: NotificationResult = {
      success: false, // Começamos como false e atualizamos para true se algo for enviado com sucesso
      email: { sent: false },
      whatsApp: { sent: false }
    };

    // Generate tokens if direct links are requested
    let patientToken = "";
    let doctorToken = "";
    let patientAccessUrl = appointmentUrl;
    let doctorAccessUrl = isPlantao ? `${baseUrl}/app/plantao` : `${baseUrl}/chats/${appointment.id}`;

    if (options.useDirectLinks) {
      try {
        console.log("[NOTIFICATIONS_TOKEN] Gerando tokens para links diretos");

        // Generate tokens for direct access links
        if (patient.user.id) {
          patientToken = await generateVerificationToken({
            userId: patient.user.id,
          });
          const patientRedirectPath = isPlantao ? `/patient/zapchat/${appointment.id}` : `/app/chats/${appointment.id}`;
          patientAccessUrl = `${baseUrl}/auth/verify?token=${encodeURIComponent(patientToken)}&redirect=${patientRedirectPath}`;
          console.log("[NOTIFICATIONS_TOKEN] Token do paciente gerado com sucesso");
        } else {
          console.warn("[NOTIFICATIONS_TOKEN] ID do paciente não encontrado, não foi possível gerar token");
        }

        if (doctor.user.id) {
          doctorToken = await generateVerificationToken({
            userId: doctor.user.id,
          });
          const doctorRedirectPath = isPlantao ? `/app/plantao` : `/app/chats/${appointment.id}`;
          doctorAccessUrl = `${baseUrl}/auth/verify?token=${encodeURIComponent(doctorToken)}&redirect=${doctorRedirectPath}`;
          console.log("[NOTIFICATIONS_TOKEN] Token do médico gerado com sucesso");
        } else {
          console.warn("[NOTIFICATIONS_TOKEN] ID do médico não encontrado, não foi possível gerar token");
        }
      } catch (tokenError) {
        console.error("[NOTIFICATIONS_TOKEN_ERROR] Erro ao gerar tokens:", tokenError);
        console.error("[NOTIFICATIONS_TOKEN_ERROR_DETAILS]", {
          errorType: tokenError instanceof Error ? tokenError.constructor.name : 'Unknown',
          message: tokenError instanceof Error ? tokenError.message : String(tokenError),
          stack: tokenError instanceof Error ? tokenError.stack : undefined
        });
        // Continue even if token generation fails, using standard URLs
      }
    }

    // Format date and time for display in notifications
    const dateOptions: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    };

    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit'
    };

    const formattedDate = new Date(appointment.scheduledAt).toLocaleDateString('pt-BR', dateOptions);
    const formattedTime = new Date(appointment.scheduledAt).toLocaleTimeString('pt-BR', timeOptions);

    // 1. SEND EMAIL NOTIFICATIONS
    if (options.sendEmail) {
      // Send email to patient
      try {
        console.log("[NOTIFICATIONS_EMAIL] Iniciando envio de email para paciente:", {
          patientEmail: patient.user.email,
          appointmentId: appointment.id,
          isNewUser: patient.isNewUser
        });

        // Email context for patient
        const patientEmailContext = {
          recipientName: patient.user.name,
          doctorName: doctor.user.name,
          appointmentDate: formattedDate,
          appointmentTime: formattedTime,
          joinUrl: patientAccessUrl,
          url: patientAccessUrl,
          name: patient.user.name,
          isNewUser: patient.isNewUser,
          tempPassword: patient.tempPassword,
          consultType: appointment.type || "VIDEO"
        };

        console.log("[NOTIFICATIONS_EMAIL] Contexto do email do paciente preparado:", {
          appointmentId: appointment.id,
          templateId: patient.isNewUser ? "newPatient" : "appointmentCreated"
        });

        // Send email to patient
        try {
          await sendEmail({
            to: patient.user.email,
            templateId: patient.isNewUser ? "newPatient" : "appointmentCreated",
            context: patientEmailContext,
            locale: "pt"
          });

          result.email!.sent = true;
          console.log("[NOTIFICATIONS_EMAIL_SUCCESS] Email para paciente enviado com sucesso:", {
            patientEmail: patient.user.email,
            appointmentId: appointment.id
          });
        } catch (patientEmailSendError) {
          console.error("[NOTIFICATIONS_EMAIL_SEND_ERROR] Erro ao enviar email para paciente:", patientEmailSendError);
          console.error("[NOTIFICATIONS_EMAIL_SEND_ERROR_DETAILS]", {
            errorType: patientEmailSendError instanceof Error ? patientEmailSendError.constructor.name : 'Unknown',
            message: patientEmailSendError instanceof Error ? patientEmailSendError.message : String(patientEmailSendError),
            stack: patientEmailSendError instanceof Error ? patientEmailSendError.stack : undefined,
            patientEmail: patient.user.email
          });

          result.email!.error = patientEmailSendError instanceof Error
            ? patientEmailSendError.message
            : "Erro ao enviar email para paciente";
          result.email!.sent = false;
        }

        // Send email to doctor
        try {
          console.log("[NOTIFICATIONS_EMAIL] Iniciando envio de email para médico:", {
            doctorEmail: doctor.user.email,
            appointmentId: appointment.id
          });

          // Email context for doctor
          const doctorEmailContext = {
            recipientName: doctor.user.name,
            patientName: patient.user.name,
            appointmentDate: formattedDate,
            appointmentTime: formattedTime,
            joinUrl: doctorAccessUrl,
            isDoctor: true,
            consultType: appointment.type || "VIDEO"
          };

          // Send email to doctor
          await sendEmail({
            to: doctor.user.email,
            templateId: "appointmentCreated",
            context: doctorEmailContext,
            locale: "pt"
          });

          console.log("[NOTIFICATIONS_EMAIL_SUCCESS] Email para médico enviado com sucesso:", {
            doctorEmail: doctor.user.email,
            appointmentId: appointment.id
          });
        } catch (doctorEmailError) {
          console.error("[NOTIFICATIONS_DOCTOR_EMAIL_ERROR] Erro ao enviar email para médico:", doctorEmailError);
          console.error("[NOTIFICATIONS_DOCTOR_EMAIL_ERROR_DETAILS]", {
            errorType: doctorEmailError instanceof Error ? doctorEmailError.constructor.name : 'Unknown',
            message: doctorEmailError instanceof Error ? doctorEmailError.message : String(doctorEmailError),
            stack: doctorEmailError instanceof Error ? doctorEmailError.stack : undefined,
            doctorEmail: doctor.user.email
          });
          // Continue even if doctor email fails - o email para o paciente ainda pode ter sido enviado
        }
      } catch (emailError) {
        console.error("[NOTIFICATIONS_EMAIL_ERROR] Erro geral ao processar emails:", emailError);
        console.error("[NOTIFICATIONS_EMAIL_ERROR_DETAILS]", {
          errorType: emailError instanceof Error ? emailError.constructor.name : 'Unknown',
          message: emailError instanceof Error ? emailError.message : String(emailError),
          stack: emailError instanceof Error ? emailError.stack : undefined,
          appointmentId: appointment.id
        });

        result.email!.error = emailError instanceof Error ? emailError.message : "Erro ao enviar email";
        result.email!.sent = false;
      }
    }

    // 2. SEND WHATSAPP NOTIFICATIONS
    if (options.sendWhatsApp) {
      // Send WhatsApp to patient if phone is available
      if (patient.user.phone) {
        try {
          // Log the raw phone number for debugging
          if (process.env.NODE_ENV === 'development') {
            console.log("[NOTIFICATIONS_WHATSAPP_DEBUG] Telefone do paciente (raw):", {
              raw: patient.user.phone,
              formatted: formatPhoneForWhatsApp(patient.user.phone),
              hasCountryCode: formatPhoneForWhatsApp(patient.user.phone).startsWith("55")
            });
          }

          // Improved phone validation
          if (!patient.user.phone.match(/^\+?[0-9]{10,15}$/)) {
            console.warn("[NOTIFICATIONS_WHATSAPP_WARNING] Formato de telefone do paciente inválido:", {
              phone: patient.user.phone,
              appointmentId: appointment.id
            });

            result.whatsApp!.error = "Formato de telefone inválido";
            result.whatsApp!.sent = false;
          } else {
            // Prepare messages for patient - Break into smaller chunks
            const patientMessages: string[] = [];

            // Greeting message
            patientMessages.push(`Olá ${patient.user.name}! Sua consulta na ZapVida foi confirmada com sucesso. 🎉`);

            // Appointment details - Keep this simple
            patientMessages.push(`📅 *Detalhes da Consulta*\n\nMédico: ${doctor.user.name}\nData: ${formattedDate}\nHorário: ${formattedTime}`);

            // Access link - Separate message
            patientMessages.push(`Para acessar sua consulta, use este link:`);
            patientMessages.push(`${patientAccessUrl}`);

            // New user instructions if applicable - Break into multiple messages if needed
            if (patient.isNewUser) {

              if (patient.tempPassword) {
                patientMessages.push(`Sua senha temporária é: *${patient.tempPassword}*`);
              }

            }

            // Final message
            patientMessages.push(`Qualquer dúvida, estamos à disposição. Obrigado por escolher a ZapVida para cuidar da sua saúde! 💙`);

            console.log("[NOTIFICATIONS_WHATSAPP] Mensagens para paciente preparadas:", {
              messageCount: patientMessages.length,
              appointmentId: appointment.id
            });

            try {
              // Initialize service with timeout options
              const evolutionService = new EvolutionService();
              const patientPhone = formatPhoneForWhatsApp(patient.user.phone);

              console.log("[NOTIFICATIONS_WHATSAPP] Enviando mensagens para o paciente:", {
                phoneRaw: patient.user.phone,
                phoneFormatted: patientPhone,
                appointmentId: appointment.id,
                totalMessages: patientMessages.length
              });

              // Send messages with retry logic
              let retryCount = 0;
              const maxRetries = 2;
              let patientResponses = [];

              while (retryCount <= maxRetries) {
                try {
                  // Send fewer messages at once to avoid timeouts
                  // Split into smaller batches if there are many messages
                  if (patientMessages.length > 3) {
                    console.log("[NOTIFICATIONS_WHATSAPP] Enviando mensagens em lotes menores");

                    // Send first batch
                    const firstBatch = patientMessages.slice(0, 2);
                    const firstResponses = await evolutionService.sendMessagesWithDelay(firstBatch, patientPhone);

                    // Wait a bit longer between batches
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // Send second batch
                    const secondBatch = patientMessages.slice(2);
                    const secondResponses = await evolutionService.sendMessagesWithDelay(secondBatch, patientPhone);

                    patientResponses = [...firstResponses, ...secondResponses];
                  } else {
                    patientResponses = await evolutionService.sendMessagesWithDelay(patientMessages, patientPhone);
                  }

                  // If we get here, it worked
                  break;
                } catch (retryError) {
                  retryCount++;
                  console.warn(`[NOTIFICATIONS_WHATSAPP_RETRY] Tentativa ${retryCount}/${maxRetries} falhou:`, retryError);

                  if (retryCount > maxRetries) {
                    throw retryError;
                  }

                  // Wait before retrying
                  await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                }
              }

              result.whatsApp!.sent = true;
              result.whatsApp!.messageCount = patientResponses.length;

              console.log("[NOTIFICATIONS_WHATSAPP_SUCCESS] WhatsApp para paciente enviado com sucesso:", {
                messageCount: patientResponses.length,
                lastMessageId: patientResponses[patientResponses.length - 1]?.key?.id,
                appointmentId: appointment.id
              });

              // Send WhatsApp to doctor with similar improvements
              if (doctor.user.phone && doctor.user.phone.trim().length >= 10) {
                try {
                  console.log("[NOTIFICATIONS_WHATSAPP] Iniciando envio de WhatsApp para médico:", {
                    doctorPhone: doctor.user.phone,
                    appointmentId: appointment.id
                  });

                  const doctorPhone = formatPhoneForWhatsApp(doctor.user.phone);
                  const doctorMessages: string[] = [];

                  // Greeting message
                  doctorMessages.push(`Olá Dr(a). ${doctor.user.name}! Uma nova consulta foi agendada na ZapVida. 📋`);

                  // Appointment details - Break into smaller chunks
                  doctorMessages.push(`📅 *Detalhes da Consulta*\n\nPaciente: ${patient.user.name}\nData: ${formattedDate}\nHorário: ${formattedTime}`);

                  // Access link - Separate message
                  doctorMessages.push(`Para ver todos os detalhes e se preparar para a consulta, acesse:`);
                  doctorMessages.push(`${doctorAccessUrl}`);

                  console.log("[NOTIFICATIONS_WHATSAPP] Mensagens para médico preparadas:", {
                    doctorPhone: doctorPhone,
                    messageCount: doctorMessages.length,
                    appointmentId: appointment.id
                  });

                  // Send messages
                  // Reutilizar o mesmo serviço já inicializado para evitar múltiplas inicializações
                  const doctorResponses = await evolutionService.sendMessagesWithDelay(doctorMessages, doctorPhone);

                  console.log("[NOTIFICATIONS_WHATSAPP_SUCCESS] WhatsApp para médico enviado com sucesso:", {
                    messageCount: doctorResponses.length,
                    doctorPhone: doctorPhone,
                    appointmentId: appointment.id
                  });
                } catch (doctorWhatsAppError) {
                  console.error("[NOTIFICATIONS_DOCTOR_WHATSAPP_ERROR] Erro ao enviar WhatsApp para médico:", doctorWhatsAppError);
                  console.error("[NOTIFICATIONS_DOCTOR_WHATSAPP_ERROR_DETAILS]", {
                    errorType: doctorWhatsAppError instanceof Error ? doctorWhatsAppError.constructor.name : 'Unknown',
                    message: doctorWhatsAppError instanceof Error ? doctorWhatsAppError.message : String(doctorWhatsAppError),
                    stack: doctorWhatsAppError instanceof Error ? doctorWhatsAppError.stack : undefined,
                    doctorPhone: doctor.user.phone
                  });
                  // Continue even if doctor WhatsApp fails - o WhatsApp para o paciente ainda pode ter sido enviado
                }
              } else {
                console.warn("[NOTIFICATIONS_WHATSAPP_WARNING] Telefone do médico não encontrado ou inválido:", {
                  doctorId: doctor.id,
                  doctorPhone: doctor.user.phone || "não fornecido",
                  appointmentId: appointment.id
                });
              }
            } catch (patientWhatsAppSendError) {
              console.error("[NOTIFICATIONS_WHATSAPP_SEND_ERROR] Erro ao enviar WhatsApp para paciente:", patientWhatsAppSendError);
              console.error("[NOTIFICATIONS_WHATSAPP_SEND_ERROR_DETAILS]", {
                errorType: patientWhatsAppSendError instanceof Error ? patientWhatsAppSendError.constructor.name : 'Unknown',
                message: patientWhatsAppSendError instanceof Error ? patientWhatsAppSendError.message : String(patientWhatsAppSendError),
                stack: patientWhatsAppSendError instanceof Error ? patientWhatsAppSendError.stack : undefined,
                patientPhone: patient.user.phone
              });

              result.whatsApp!.error = patientWhatsAppSendError instanceof Error
                ? patientWhatsAppSendError.message
                : "Erro ao enviar WhatsApp para paciente";
              result.whatsApp!.sent = false;
            }
          }
        } catch (whatsAppError) {
            console.error("[NOTIFICATIONS_WHATSAPP_ERROR] Erro geral ao processar WhatsApp:", whatsAppError);
            console.error("[NOTIFICATIONS_WHATSAPP_ERROR_DETAILS]", {
              errorType: whatsAppError instanceof Error ? whatsAppError.constructor.name : 'Unknown',
              message: whatsAppError instanceof Error ? whatsAppError.message : String(whatsAppError),
              stack: whatsAppError instanceof Error ? whatsAppError.stack : undefined,
              appointmentId: appointment.id
            });

            result.whatsApp!.error = whatsAppError instanceof Error ? whatsAppError.message : "Erro ao enviar WhatsApp";
            result.whatsApp!.sent = false;
          }
        } else {
          // Improved logging for missing phone
          console.warn("[NOTIFICATIONS_WHATSAPP_WARNING] Telefone do paciente não encontrado:", {
            patientId: patient.id,
            patientEmail: patient.user.email,
            patientPhone: patient.user.phone,
            appointmentId: appointment.id
          });

          result.whatsApp!.error = "Telefone do paciente não encontrado";
          result.whatsApp!.sent = false;
        }
      }

    // Determine overall success (at least one notification type succeeded)
    result.success = result.email!.sent || result.whatsApp!.sent;

    console.log("[NOTIFICATIONS_COMPLETE] Processo de notificações finalizado:", {
      success: result.success,
      emailSent: result.email!.sent,
      whatsAppSent: result.whatsApp!.sent,
      appointmentId: appointment.id
    });

    return result;
  } catch (error) {
    console.error("[NOTIFICATIONS_FATAL_ERROR] Erro crítico no processo de notificações:", error);
    console.error("[NOTIFICATIONS_FATAL_ERROR_DETAILS]", {
      errorType: error instanceof Error ? error.constructor.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      appointmentId: appointment.id
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido ao enviar notificações",
      email: { sent: false, error: "Erro no processamento geral" },
      whatsApp: { sent: false, error: "Erro no processamento geral" }
    };
  }
}
