"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { AccessToken } from "livekit-server-sdk";

const LIVEKIT_API_KEY = process.env.LIVEKIT_API_KEY;
const LIVEKIT_API_SECRET = process.env.LIVEKIT_API_SECRET;
const LIVEKIT_URL = process.env.NEXT_PUBLIC_LIVEKIT_URL;

export async function getLiveKitToken(appointmentId: string) {
	try {
		console.log("[LiveKit] Starting token generation for appointment:", appointmentId);

		if (!LIVEKIT_API_KEY || !LIVEKIT_API_SECRET || !LIVEKIT_URL) {
			console.error("[LiveKit] Configuration missing:", {
				hasApiKey: !!LIVEKIT_API_KEY,
				hasApiSecret: !!LIVEKIT_API_SECRET,
				hasUrl: !!LIVEKIT_URL
			});
			throw new Error("LiveKit configuration missing");
		}

		const session = await currentUser();
		if (!session?.user) {
			console.error("[LiveKit] No user session found");
			throw new Error("Não autorizado");
		}

		console.log("[LiveKit] User session found:", {
			userId: session.user.id,
			userName: session.user.name,
			userRole: session.user.role
		});

		// Buscar a consulta usando Prisma
		const appointment = await db.appointment.findUnique({
			where: { id: appointmentId },
			select: {
				id: true,
				doctor: {
					select: {
						user: {
							select: {
								id: true,
								name: true,
								email: true
							}
						}
					}
				},
				patient: {
					select: {
						user: {
							select: {
								id: true,
								name: true,
								email: true
							}
						}
					}
				}
			}
		});

		if (!appointment) {
			console.error("[LiveKit] Appointment not found:", appointmentId);
			throw new Error("Consulta não encontrada");
		}

		console.log("[LiveKit] Appointment found:", {
			id: appointment.id,
			doctorUserId: appointment.doctor.user.id,
			patientUserId: appointment.patient.user.id
		});

		const isDoctor = appointment.doctor.user.id === session.user.id;
		const isPatient = appointment.patient.user.id === session.user.id;
		const isAdmin = session.user.role === "ADMIN";

		// Verificar se o usuário tem permissão para acessar a consulta
		if (!isDoctor && !isPatient && !isAdmin) {
			console.error("[LiveKit] Access denied:", {
				userId: session.user.id,
				isDoctor,
				isPatient,
				isAdmin,
				appointmentId
			});
			throw new Error("Acesso não autorizado");
		}

		console.log("[LiveKit] Access granted:", {
			userId: session.user.id,
			role: isDoctor ? "DOCTOR" : isPatient ? "PATIENT" : "ADMIN",
			appointmentId
		});

		const roomName = `appointment-${appointmentId}`;

		// Criar token como string
		const at = new AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET, {
			identity: session.user.id,
			name: session.user.name ?? "Usuário",
		});

		// Adicionar permissões
		at.addGrant({
			room: roomName,
			roomJoin: true,
			canPublish: true,
			canSubscribe: true,
			canPublishData: true,
		});

		// Gerar token como string
		const token = await at.toJwt();
		
		// Verificar se o token é válido
		if (!token || typeof token !== 'string') {
			console.error("[LiveKit] Token inválido gerado:", {
				token,
				tokenType: typeof token,
				tokenLength: token ? token.length : 0
			});
			throw new Error("Token inválido gerado");
		}
		
		// Garantir que o token é uma string válida
		const tokenString = String(token);

		// Preparar URL do servidor - garantir que sempre tenha o protocolo correto
		let serverUrl = LIVEKIT_URL;
		if (!serverUrl.startsWith("wss://") && !serverUrl.startsWith("ws://")) {
			serverUrl = `wss://${serverUrl}`;
		}

		console.log("[LiveKit] Token generation completed:", {
			room: roomName,
			identity: session.user.id,
			role: isDoctor ? "DOCTOR" : "PATIENT",
			hasToken: typeof token === "string",
			tokenLength: token ? token.length : 0,
			url: serverUrl,
			apiKeyPrefix: LIVEKIT_API_KEY ? LIVEKIT_API_KEY.substring(0, 8) + "..." : "missing",
			apiSecretPrefix: LIVEKIT_API_SECRET ? LIVEKIT_API_SECRET.substring(0, 8) + "..." : "missing",
		});

		return {
			token: tokenString,
			url: serverUrl,
			room: roomName,
		};
	} catch (error) {
		console.error("[LiveKit] Error generating token:", {
			error: error instanceof Error ? error.message : 'Erro desconhecido',
			stack: error instanceof Error ? error.stack : undefined,
			appointmentId
		});
		throw error;
	}
}
