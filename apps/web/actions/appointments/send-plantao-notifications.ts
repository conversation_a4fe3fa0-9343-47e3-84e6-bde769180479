"use server";

import { db } from "database";
import { formatPhoneForWhatsApp } from "../../lib/utils/format-phone";
import { EvolutionService } from "../../actions/checkout/integrations/evolution/evolution.service";

/**
 * Sends a WhatsApp notification to a patient when their on-duty appointment payment is confirmed
 */
export async function sendPlantaoPaymentConfirmedNotification(appointmentId: string) {
  try {
    console.log("[PLANTAO_NOTIFICATION] Sending payment confirmation to patient:", appointmentId);

    // 1. Get appointment data with patient and doctor info
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        patient: {
          include: {
            user: true
          }
        },
        doctor: {
          include: {
            user: true
          }
        }
      }
    });

    if (!appointment) {
      console.error("[PLANTAO_NOTIFICATION] Appointment not found:", appointmentId);
      return { success: false, error: "Appointment not found" };
    }

    // Check if patient has a phone number
    if (!appointment.patient.user.phone) {
      console.warn("[PLANTAO_NOTIFICATION] Patient has no phone number:", {
        patientId: appointment.patient.id,
        patientName: appointment.patient.user.name,
        patientEmail: appointment.patient.user.email || "N/A"
      });

      // Return a specific response for missing phone
      return {
        success: false,
        error: "Telefone do paciente não encontrado",
        fallback: "Notificação apenas no sistema"
      };
    }

    // 2. Generate access URL
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://zapvida.com";
    const accessUrl = `${baseUrl}/plantao/status/${appointment.id}`;

    // 3. Send WhatsApp notification using Evolution service directly
    try {
      const phone = formatPhoneForWhatsApp(appointment.patient.user.phone);
      const evolutionService = new EvolutionService();

      const messages = [
        `🩺 *Pagamento Confirmado - Plantão ZapVida*\n\nOlá ${appointment.patient.user.name}!`,
        `✅ Seu pagamento para o atendimento de plantão foi confirmado com sucesso.`,
        `🔗 *Para acompanhar o status e aguardar um médico, acesse:*\n${accessUrl}`,
        `⏰ *Tempo estimado de espera:* 5-15 minutos\n\nA equipe ZapVida agradece sua confiança! 💙`
      ];

      const responses = await evolutionService.sendMessagesWithDelay(messages, phone);

      const successfulMessages = responses.filter(r => r && r.key && r.key.id);

      if (successfulMessages.length > 0) {
        return { success: true, data: { sent: true, count: successfulMessages.length } };
      } else {
        return { success: false, error: "No messages were sent successfully" };
      }
    } catch (whatsappError) {
      console.error("[PLANTAO_NOTIFICATION] Error sending WhatsApp:", whatsappError);
      return { success: false, error: "Error sending WhatsApp notification" };
    }
  } catch (error) {
    console.error("[PLANTAO_NOTIFICATION] Error sending payment confirmation:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Sends a WhatsApp notification to a patient when a doctor accepts their on-duty appointment
 */
export async function sendPlantaoAcceptedNotification(appointmentId: string) {
  try {
    console.log("[PLANTAO_NOTIFICATION] Starting acceptance notification process:", appointmentId);

    // 1. Get appointment data with patient and doctor info
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        patient: {
          include: {
            user: true
          }
        },
        doctor: {
          include: {
            user: true,
            specialties: true
          }
        },
        acceptedByDoctor: {
          include: {
            user: true,
            specialties: true
          }
        }
      }
    });

    console.log("[PLANTAO_NOTIFICATION] Appointment data retrieved:", {
      found: !!appointment,
      patientName: appointment?.patient?.user?.name,
      patientPhone: appointment?.patient?.user?.phone ? "***" + appointment.patient.user.phone.slice(-4) : "none",
      doctorName: appointment?.doctor?.user?.name,
      acceptedByDoctorName: appointment?.acceptedByDoctor?.user?.name
    });

    if (!appointment) {
      console.error("[PLANTAO_NOTIFICATION] Appointment not found:", appointmentId);
      return { success: false, error: "Appointment not found" };
    }

    // Get the doctor who accepted (might be different from the original doctor)
    const doctor = appointment.acceptedByDoctor || appointment.doctor;

    if (!doctor) {
      console.error("[PLANTAO_NOTIFICATION] No doctor found for appointment:", appointmentId);
      return { success: false, error: "No doctor found for appointment" };
    }

    console.log("[PLANTAO_NOTIFICATION] Using doctor:", {
      doctorId: doctor.id,
      doctorName: doctor.user.name,
      hasSpecialties: !!doctor.specialties?.length
    });

    // 2. Generate access URL - Use magic link for plantão
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://zapvida.com";
    
    // Try to generate magic link first
    let accessUrl = `${baseUrl}/patient/zapchat/${appointment.id}`;
    try {
      const magicLinkResponse = await fetch(`${baseUrl}/api/auth/magic-link`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          appointmentId: appointment.id,
          type: 'PLANTAO'
        })
      });
      
      if (magicLinkResponse.ok) {
        const magicLinkData = await magicLinkResponse.json();
        if (magicLinkData.success && magicLinkData.magicUrl) {
          accessUrl = magicLinkData.magicUrl;
          console.log("[PLANTAO_NOTIFICATION] Magic link gerado com sucesso");
        }
      }
    } catch (magicLinkError) {
      console.error("[PLANTAO_NOTIFICATION] Erro ao gerar magic link:", magicLinkError);
    }

    console.log("[PLANTAO_NOTIFICATION] Generated access URL:", accessUrl);

    // 3. Check if patient has phone
    if (!appointment.patient.user.phone) {
      console.warn("[PLANTAO_NOTIFICATION] Patient has no phone number:", appointment.patient.id);
      return { success: false, error: "Patient has no phone number" };
    }

    // 4. Get doctor's main specialty if available
    let doctorSpecialty = "";
    if (doctor.specialties && doctor.specialties.length > 0) {
      doctorSpecialty = doctor.specialties[0].name;
      console.log("[PLANTAO_NOTIFICATION] Doctor specialty found:", doctorSpecialty);
    }

    // 5. Prepare the messages for WhatsApp
    const messages: string[] = [];

    // Greeting with emoji
    messages.push(`🩺 *Ótimas notícias, ${appointment.patient.user.name}!*\n\nSeu atendimento de plantão foi aceito por um médico e começará em instantes.`);

    // Doctor information with specialty if available
    if (doctorSpecialty) {
      messages.push(`👨‍⚕️ *Seu médico:* Dr(a). ${doctor.user.name}\n*Especialidade:* ${doctorSpecialty}`);
    } else {
      messages.push(`👨‍⚕️ *Seu médico:* Dr(a). ${doctor.user.name}`);
    }

    // Instructions to join
    messages.push(`🔗 *Para iniciar a consulta agora, clique no link abaixo:*\n${accessUrl}`);

    // Tips for a good consultation
    messages.push(`✅ *Dicas para um bom atendimento:*\n• Esteja em um local calmo e com boa conexão\n• Tenha seus documentos médicos em mãos\n• Prepare uma lista dos seus sintomas\n• Anote suas dúvidas para não esquecer`);

    // Final message
    messages.push(`A equipe ZapVida agradece sua confiança! Se precisar de ajuda, estamos à disposição. 💙`);

    console.log("[PLANTAO_NOTIFICATION] Prepared messages:", {
      messageCount: messages.length,
      totalChars: messages.reduce((acc, msg) => acc + msg.length, 0)
    });

    // 6. Send the WhatsApp notification
    try {
      console.log("[PLANTAO_NOTIFICATION] Starting WhatsApp sending process");

      // Format phone number
      const phone = formatPhoneForWhatsApp(appointment.patient.user.phone);
      console.log("[PLANTAO_NOTIFICATION] Phone formatted:", {
        original: appointment.patient.user.phone,
        formatted: phone
      });

      // Check environment variables
      const envCheck = {
        hasEvolutionUrl: !!process.env.EVOLUTION_URL,
        hasEvolutionInstance: !!process.env.EVOLUTION_INSTANCE,
        hasEvolutionApiKey: !!process.env.EVOLUTION_API_KEY,
        evolutionUrl: process.env.EVOLUTION_URL
      };
      console.log("[PLANTAO_NOTIFICATION] Environment check:", envCheck);

      if (!envCheck.hasEvolutionUrl || !envCheck.hasEvolutionInstance || !envCheck.hasEvolutionApiKey) {
        console.error("[PLANTAO_NOTIFICATION] Missing Evolution API configuration");
        return {
          success: false,
          error: "Evolution API not configured properly",
          details: envCheck
        };
      }

      // Initialize service
      console.log("[PLANTAO_NOTIFICATION] Initializing Evolution service");
      const evolutionService = new EvolutionService();

      // Send messages with delay between them
      console.log("[PLANTAO_NOTIFICATION] Sending messages via Evolution API");
      const responses = await evolutionService.sendMessagesWithDelay(messages, phone);

      console.log("[PLANTAO_NOTIFICATION] Evolution API responses received:", {
        responseCount: responses.length,
        successfulResponses: responses.filter(r => r && r.key && r.key.id).length,
        errorResponses: responses.filter(r => !r || !r.key || !r.key.id).length
      });

      // Check if any messages were sent successfully
      const successfulMessages = responses.filter(r => r && r.key && r.key.id);

      if (successfulMessages.length > 0) {
        console.log("[PLANTAO_NOTIFICATION] WhatsApp messages sent successfully:", {
          messageCount: successfulMessages.length,
          appointmentId: appointment.id,
          messageIds: successfulMessages.map(r => r.key.id)
        });

        return { success: true, data: { sent: true, count: successfulMessages.length } };
      } else {
        console.error("[PLANTAO_NOTIFICATION] No messages were sent successfully");
        return {
          success: false,
          error: "No messages were sent successfully",
          details: { responses }
        };
      }

    } catch (whatsappError) {
      console.error("[PLANTAO_NOTIFICATION] Error in WhatsApp sending process:", whatsappError);
      return {
        success: false,
        error: whatsappError instanceof Error ? whatsappError.message : "Error sending WhatsApp",
        details: whatsappError instanceof Error ? whatsappError.stack : undefined
      };
    }
  } catch (error) {
    console.error("[PLANTAO_NOTIFICATION] Error in acceptance notification process:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      details: error instanceof Error ? error.stack : undefined
    };
  }
}

/**
 * Sends a WhatsApp notification to a doctor about a new on-duty appointment
 */
export async function sendPlantaoDoctorNotification(
  doctorId: string,
  appointmentId: string,
  patientName: string,
  urgencyLevel: string
) {
  try {
    console.log("[PLANTAO_NOTIFICATION] Sending notification to doctor:", doctorId);

    // 1. Get doctor data
    const doctor = await db.doctor.findUnique({
      where: { id: doctorId },
      include: {
        user: true
      }
    });

    if (!doctor) {
      console.error("[PLANTAO_NOTIFICATION] Doctor not found:", doctorId);
      return { success: false, error: "Doctor not found" };
    }

    // 2. Check if doctor has phone
    if (!doctor.user.phone) {
      console.warn("[PLANTAO_NOTIFICATION] Doctor has no phone number:", doctorId);
      return { success: false, error: "Doctor has no phone number" };
    }

    // 3. Generate access URL
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://zapvida.com";
    const accessUrl = `${baseUrl}/app/plantao`;

    // 4. Format date and time
    const now = new Date();
    const formattedDate = now.toLocaleDateString('pt-BR');
    const formattedTime = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });

    // 5. Map urgency level to label
    const urgencyLabels: Record<string, string> = {
      "HIGH": "🔴 Muito Urgente",
      "MEDIUM": "🟡 Urgente",
      "LOW": "🟢 Pouco Urgente"
    };

    // 6. Send WhatsApp notification using Evolution service directly
    try {
      const phone = formatPhoneForWhatsApp(doctor.user.phone);
      const evolutionService = new EvolutionService();

      const messages = [
        `🩺 *Novo Atendimento de Plantão - ZapVida*\n\nOlá Dr(a). ${doctor.user.name}!`,
        `📋 *Detalhes do Paciente:*\n• Nome: ${patientName}\n• Nível de Urgência: ${urgencyLabels[urgencyLevel] || urgencyLevel}`,
        `📅 *Data/Hora:* ${formattedDate} às ${formattedTime}`,
        `🔗 *Para aceitar o atendimento, acesse:*\n${accessUrl}`,
        `⏰ *Importante:* Este é um atendimento de plantão que requer resposta imediata.`
      ];

      const responses = await evolutionService.sendMessagesWithDelay(messages, phone);

      const successfulMessages = responses.filter(r => r && r.key && r.key.id);

      if (successfulMessages.length > 0) {
        return { success: true, data: { sent: true, count: successfulMessages.length } };
      } else {
        return { success: false, error: "No messages were sent successfully" };
      }
    } catch (whatsappError) {
      console.error("[PLANTAO_NOTIFICATION] Error sending WhatsApp to doctor:", whatsappError);
      return { success: false, error: "Error sending WhatsApp notification to doctor" };
    }
  } catch (error) {
    console.error("[PLANTAO_NOTIFICATION] Error sending doctor notification:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Sends a WhatsApp notification to a patient when they schedule a regular consultation
 */
export async function sendConsultationScheduledNotification(appointmentId: string) {
  try {
    console.log("[CONSULTATION_NOTIFICATION] Sending consultation scheduled notification:", appointmentId);

    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        patient: {
          include: {
            user: true
          }
        },
        doctor: {
          include: {
            user: true,
            specialties: true
          }
        }
      }
    });

    if (!appointment) {
      return { success: false, error: "Appointment not found" };
    }

    if (!appointment.patient.user.phone) {
      return { success: false, error: "Patient has no phone number" };
    }

    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://zapvida.com";
    const accessUrl = `${baseUrl}/chats/${appointment.id}`;
    const scheduledDate = appointment.scheduledAt.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    const scheduledTime = appointment.scheduledAt.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });

    try {
      const phone = formatPhoneForWhatsApp(appointment.patient.user.phone);
      const evolutionService = new EvolutionService();

      const messages = [
        `🩺 *Consulta Agendada - ZapVida*\n\nOlá ${appointment.patient.user.name}!`,
        `✅ Sua consulta foi agendada com sucesso!`,
        `👨‍⚕️ *Médico:* Dr(a). ${appointment.doctor?.user.name || "Médico"}`,
        `📅 *Data:* ${scheduledDate}`,
        `⏰ *Horário:* ${scheduledTime}`,
        `🔗 *Para acessar a consulta:*\n${accessUrl}`,
        `📋 *Lembre-se:*\n• Chegue com 10 minutos de antecedência\n• Tenha seus documentos em mãos\n• Em caso de cancelamento, avise com 24h de antecedência`
      ];

      const responses = await evolutionService.sendMessagesWithDelay(messages, phone);
      const successfulMessages = responses.filter(r => r && r.key && r.key.id);

      if (successfulMessages.length > 0) {
        return { success: true, data: { sent: true, count: successfulMessages.length } };
      } else {
        return { success: false, error: "No messages were sent successfully" };
      }
    } catch (whatsappError) {
      console.error("[CONSULTATION_NOTIFICATION] Error sending WhatsApp:", whatsappError);
      return { success: false, error: "Error sending WhatsApp notification" };
    }
  } catch (error) {
    console.error("[CONSULTATION_NOTIFICATION] Error:", error);
    return { success: false, error: "Unknown error" };
  }
}

/**
 * Sends a WhatsApp notification to a patient when they subscribe to a plan
 */
export async function sendSubscriptionConfirmedNotification(subscriptionId: string) {
  try {
    console.log("[SUBSCRIPTION_NOTIFICATION] Sending subscription confirmation:", subscriptionId);

    const subscription = await db.patientSubscription.findUnique({
      where: { id: subscriptionId },
      include: {
        patient: {
          include: {
            user: true
          }
        },
        plan: true
      }
    });

    if (!subscription) {
      return { success: false, error: "Subscription not found" };
    }

    if (!subscription.patient.user.phone) {
      return { success: false, error: "Patient has no phone number" };
    }

    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://zapvida.com";
    const accessUrl = `${baseUrl}/patient/dashboard`;

    try {
      const phone = formatPhoneForWhatsApp(subscription.patient.user.phone);
      const evolutionService = new EvolutionService();

      const messages = [
        `🎉 *Assinatura Ativada - ZapVida*\n\nOlá ${subscription.patient.user.name}!`,
        `✅ Sua assinatura foi ativada com sucesso!`,
        `📋 *Plano:* ${subscription.plan?.name || "Plano de Saúde"}`,
        `💰 *Valor:* R$ ${subscription.plan?.price || "0,00"}/mês`,
        `🔗 *Acesse sua área do paciente:*\n${accessUrl}`,
        `📱 *Benefícios:*\n• Consultas ilimitadas\n• Atendimento prioritário\n• Suporte 24/7\n• Descontos em exames`,
        `A equipe ZapVida agradece sua confiança! 💙`
      ];

      const responses = await evolutionService.sendMessagesWithDelay(messages, phone);
      const successfulMessages = responses.filter(r => r && r.key && r.key.id);

      if (successfulMessages.length > 0) {
        return { success: true, data: { sent: true, count: successfulMessages.length } };
      } else {
        return { success: false, error: "No messages were sent successfully" };
      }
    } catch (whatsappError) {
      console.error("[SUBSCRIPTION_NOTIFICATION] Error sending WhatsApp:", whatsappError);
      return { success: false, error: "Error sending WhatsApp notification" };
    }
  } catch (error) {
    console.error("[SUBSCRIPTION_NOTIFICATION] Error:", error);
    return { success: false, error: "Unknown error" };
  }
}
