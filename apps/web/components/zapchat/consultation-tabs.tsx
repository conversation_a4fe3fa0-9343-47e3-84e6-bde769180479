"use client";

import { useState } from "react";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";

interface ConsultationTabsProps {
  consultations: any[];
  onTabChange: (tab: string) => void;
  activeTab: string;
  isLoading?: boolean;
}

export function ConsultationTabs({
  consultations,
  onTabChange,
  activeTab,
  isLoading = false
}: ConsultationTabsProps) {
  // Contar consultas por status
  const getConsultationCounts = () => {
    const counts = {
      in_progress: consultations.filter(c => c.status === 'IN_PROGRESS' || c.status === 'SCHEDULED' || c.is_on_duty).length,
      completed: consultations.filter(c => c.status === 'COMPLETED').length
    };
    return counts;
  };

  const counts = getConsultationCounts();

  const tabs = [
    {
      id: 'in_progress',
      label: 'Em Andamento',
      description: 'Consultas e plantões ativos'
    },
    {
      id: 'completed',
      label: 'Finalizadas',
      description: 'Consultas concluídas'
    }
  ];

  return (
    <div className="border-b border-gray-200 bg-white">
      <div className="flex">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              "flex-1 min-w-0 px-6 py-5 text-sm font-medium border-b-2 transition-all duration-200 relative group",
              "hover:bg-gray-50/50",
              activeTab === tab.id
                ? "border-blue-500 text-blue-600 bg-blue-50/30"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            )}
          >
            <div className="flex flex-col items-center gap-2 min-h-[70px] justify-center">
              <div className="flex items-center gap-3">
                <span className="truncate font-semibold text-base">{tab.label}</span>
              </div>
              <span className="text-sm text-gray-400 truncate max-w-full leading-tight">
                {tab.description}
              </span>
            </div>

            {/* Indicador de loading */}
            {isLoading && activeTab === tab.id && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 animate-pulse" />
            )}
          </button>
        ))}
      </div>
    </div>
  );
}
