/* Animações personalizadas para chamada de áudio */

@keyframes pulse-ring {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }

    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

@keyframes pulse-dot {

    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {

    0%,
    100% {
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
    }

    50% {
        box-shadow: 0 0 40px rgba(34, 197, 94, 0.6);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }

    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Classes de animação */
.audio-call-pulse-ring {
    animation: pulse-ring 2s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

.audio-call-pulse-dot {
    animation: pulse-dot 2s ease-in-out infinite;
}

.audio-call-float {
    animation: float 3s ease-in-out infinite;
}

.audio-call-glow {
    animation: glow 2s ease-in-out infinite;
}

.audio-call-ripple {
    animation: ripple 1s ease-out infinite;
}

/* Efeitos de hover para botões */
.audio-call-button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.audio-call-button:hover {
    transform: translateY(-2px) scale(1.05);
}

.audio-call-button:active {
    transform: translateY(0) scale(0.95);
}

/* Gradientes animados */
.audio-call-bg-gradient {
    background: linear-gradient(-45deg, #0f172a, #1e293b, #334155, #475569);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/* Efeito de partículas flutuantes */
.audio-call-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(34, 197, 94, 0.6);
    border-radius: 50%;
    animation: float-particle 6s ease-in-out infinite;
}

@keyframes float-particle {

    0%,
    100% {
        transform: translateY(0) translateX(0);
        opacity: 0;
    }

    10% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    50% {
        transform: translateY(-100px) translateX(50px);
    }
}

/* Responsividade para mobile */
@media (max-width: 768px) {
    .audio-call-pulse-ring {
        animation-duration: 1.5s;
    }

    .audio-call-float {
        animation-duration: 2s;
    }

    .audio-call-button:hover {
        transform: none;
    }

    .audio-call-button:active {
        transform: scale(0.95);
    }
}

/* Modo escuro otimizado */
@media (prefers-color-scheme: dark) {
    .audio-call-bg-gradient {
        background: linear-gradient(-45deg, #0a0a0a, #1a1a1a, #2a2a2a, #3a3a3a);
    }
}

/* Acessibilidade - reduzir animações se preferido */
@media (prefers-reduced-motion: reduce) {

    .audio-call-pulse-ring,
    .audio-call-pulse-dot,
    .audio-call-float,
    .audio-call-glow,
    .audio-call-ripple,
    .audio-call-bg-gradient {
        animation: none;
    }

    .audio-call-button {
        transition: none;
    }
}