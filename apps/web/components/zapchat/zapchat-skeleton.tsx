"use client";

import { cn } from "@ui/lib";
import { MessageCir<PERSON>, ArrowLeft, RefreshCw } from "lucide-react";

interface ZapChatSkeletonProps {
  isMobile?: boolean;
  userRole?: "DOCTOR" | "PATIENT";
}

export function ZapChatSkeleton({ isMobile = false, userRole = "PATIENT" }: ZapChatSkeletonProps) {
  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header Skeleton - apenas para pacientes */}
      {userRole === "PATIENT" && (
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
              <div className="w-24 h-6 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>
      )}

      {/* Main Layout */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar Skeleton */}
        <div className={cn(
          "bg-white border-r border-gray-200 flex flex-col",
          isMobile ? "w-full" : "w-96 xl:w-[28rem] 2xl:w-[32rem] flex-shrink-0"
        )}>
          {/* Sidebar Header */}
          <div className="p-3 lg:p-4 border-b border-gray-200 bg-white">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
                <div className="w-16 h-4 bg-gray-200 rounded animate-pulse" />
              </div>
              <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
            </div>

            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
              <div>
                <div className="w-20 h-5 bg-gray-200 rounded animate-pulse mb-1" />
                <div className="w-32 h-4 bg-gray-200 rounded animate-pulse" />
              </div>
            </div>
          </div>

          {/* Tabs Skeleton */}
          <div className="border-b border-gray-200 bg-white">
            <div className="flex">
              {['Em Andamento', 'Finalizadas'].map((tab, index) => (
                <div key={tab} className="flex-1 px-6 py-5">
                  <div className="flex flex-col items-center gap-2 min-h-[70px] justify-center">
                    <div className="flex items-center gap-3">
                      <div className="h-5 bg-gray-200 rounded w-24 animate-pulse" />
                    </div>
                    <div className="h-4 bg-gray-200 rounded w-32 animate-pulse" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Consultation List Skeleton */}
          <div className="flex-1 overflow-y-auto p-3 lg:p-4 space-y-3">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-50 rounded-lg p-4 space-y-3">
                {/* Header da consulta */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
                    <div>
                      <div className="w-24 h-4 bg-gray-200 rounded animate-pulse mb-1" />
                      <div className="w-16 h-3 bg-gray-200 rounded animate-pulse" />
                    </div>
                  </div>
                  <div className="w-16 h-6 bg-gray-200 rounded-full animate-pulse" />
                </div>
                
                {/* Última mensagem */}
                <div className="space-y-2">
                  <div className="w-full h-3 bg-gray-200 rounded animate-pulse" />
                  <div className="w-3/4 h-3 bg-gray-200 rounded animate-pulse" />
                </div>
                
                {/* Timestamp */}
                <div className="flex justify-between items-center">
                  <div className="w-12 h-3 bg-gray-200 rounded animate-pulse" />
                  <div className="w-4 h-4 bg-gray-200 rounded-full animate-pulse" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Chat Area Skeleton - Only show on desktop */}
        {!isMobile && (
          <div className="flex-1 min-w-0 bg-white border-l border-gray-200">
            {/* Chat Header Skeleton */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
                  <div>
                    <div className="w-32 h-5 bg-gray-200 rounded animate-pulse mb-1" />
                    <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
                  </div>
                </div>
                <div className="flex gap-2">
                  <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
                  <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
                  <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
                </div>
              </div>
            </div>

            {/* Messages Area Skeleton */}
            <div className="flex-1 overflow-y-auto p-6 space-y-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className={cn(
                  "flex gap-3",
                  i % 2 === 0 ? "flex-row-reverse" : "flex-row"
                )}>
                  <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse flex-shrink-0" />
                  <div className={cn(
                    "max-w-[70%] flex flex-col",
                    i % 2 === 0 ? "items-end" : "items-start"
                  )}>
                    <div className={cn(
                      "rounded-2xl px-4 py-3 shadow-sm",
                      i % 2 === 0 ? "bg-gray-200" : "bg-gray-100"
                    )}>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-300 rounded w-48 animate-pulse" />
                        {i % 3 === 0 && <div className="h-4 bg-gray-300 rounded w-32 animate-pulse" />}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 mt-2 px-1">
                      <div className="h-3 w-16 bg-gray-200 rounded animate-pulse" />
                      {i % 2 === 0 && <div className="w-3 h-3 bg-gray-200 rounded-full animate-pulse" />}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Input Area Skeleton */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
                <div className="flex-1 h-10 bg-gray-200 rounded-full animate-pulse" />
                <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Skeleton específico para mobile quando não há consulta selecionada
export function ZapChatMobileSkeleton() {
  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header Skeleton */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
            <div className="w-24 h-6 bg-gray-200 rounded animate-pulse" />
          </div>
          <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Full width consultation list */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg p-4 shadow-sm space-y-3">
            {/* Header da consulta */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse" />
                <div>
                  <div className="w-28 h-5 bg-gray-200 rounded animate-pulse mb-1" />
                  <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
                </div>
              </div>
              <div className="w-20 h-6 bg-gray-200 rounded-full animate-pulse" />
            </div>
            
            {/* Última mensagem */}
            <div className="space-y-2">
              <div className="w-full h-4 bg-gray-200 rounded animate-pulse" />
              <div className="w-4/5 h-4 bg-gray-200 rounded animate-pulse" />
            </div>
            
            {/* Timestamp e status */}
            <div className="flex justify-between items-center">
              <div className="w-16 h-3 bg-gray-200 rounded animate-pulse" />
              <div className="w-5 h-5 bg-gray-200 rounded-full animate-pulse" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
