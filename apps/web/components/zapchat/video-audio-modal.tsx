"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@ui/components/dialog";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import {
  Video,
  VideoOff,
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  Settings,
  X,
  XCircle,
  Loader2,
  AlertCircle,
  RefreshCw,
  Signal,
  Wifi,
  WifiOff,
  CheckCircle,
  Clock,
  Users
} from "lucide-react";
import { toast } from "sonner";
import { LiveKitRoom, VideoConference, useLocalParticipant } from "@livekit/components-react";
import { Room, Track, RoomEvent } from "livekit-client";
import "@livekit/components-styles";
import { LiveKitDebugPanel } from "./livekit-debug-panel";
import { MediaPermissionGuide } from "./media-permission-guide";
import { MultiParticipantAudioCall } from "./multi-participant-audio-call";
import "./audio-call-animations.css";

interface VideoAudioModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointmentId: string;
  userRole: "DOCTOR" | "PATIENT";
  mode: "video" | "audio";
  otherParticipantName: string;
  onError?: (error: Error) => void;
}

interface LiveKitTokenResponse {
  token: string;
  serverUrl: string;
  roomName: string;
  identity: string;
}

export function VideoAudioModal({
  isOpen,
  onClose,
  appointmentId,
  userRole,
  mode,
  otherParticipantName,
  onError
}: VideoAudioModalProps) {
  const [token, setToken] = useState<string | null>(null);
  const [serverUrl, setServerUrl] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<Error | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [callDuration, setCallDuration] = useState(0);
  const [connectionQuality, setConnectionQuality] = useState<'excellent' | 'good' | 'poor'>('good');
  const [permissionState, setPermissionState] = useState<'idle' | 'requesting' | 'granted' | 'denied'>('idle');
  const [permissionError, setPermissionError] = useState<string | null>(null);
  const [currentMode, setCurrentMode] = useState<"video" | "audio">(mode); // Modo atual (pode mudar para fallback)
  const [isFallbackMode, setIsFallbackMode] = useState(false); // Indica se está em modo fallback
  const [participants, setParticipants] = useState<Array<{
    id: string;
    name: string;
    isMuted: boolean;
    isSpeaking: boolean;
    isLocal: boolean;
  }>>([]);

  const maxReconnectAttempts = 3;
  const callStartTimeRef = useRef<Date | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Função para obter token do LiveKit
  const getLiveKitToken = useCallback(async (): Promise<LiveKitTokenResponse> => {
    try {
      const response = await fetch('/api/livekit/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appointmentId,
          userRole,
          mode
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.token || !data.serverUrl) {
        throw new Error('Token ou ServerUrl não recebidos');
      }

      // Validar se a URL é válida
      try {
        new URL(data.serverUrl);
      } catch {
        throw new Error('URL do servidor LiveKit inválida');
      }

      return data;
    } catch (error) {
      console.error('Erro ao obter token LiveKit:', error);
      throw new Error(error instanceof Error ? error.message : 'Não foi possível obter token de conexão');
    }
  }, [appointmentId, userRole, mode]);

  // Solicitar permissões de mídia com fallback automático para áudio
  const requestMediaPermissions = useCallback(async (forceFallback = false) => {
    setPermissionState('requesting');
    setPermissionError(null);

    const targetMode = forceFallback ? "audio" : currentMode;

    try {
      console.log("🎤 Solicitando permissões de mídia para modo:", targetMode);

      // Verificar se o navegador suporta getUserMedia
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Seu navegador não suporta acesso à mídia. Use um navegador moderno como Chrome, Firefox ou Safari.");
      }

      // Verificar se já temos permissões
      try {
        const permissions = await Promise.all([
          navigator.permissions.query({ name: 'camera' as PermissionName }).catch(() => null),
          navigator.permissions.query({ name: 'microphone' as PermissionName }).catch(() => null)
        ]);

        const [cameraPermission, micPermission] = permissions;
        console.log("📋 Status atual das permissões:", {
          camera: cameraPermission?.state,
          microphone: micPermission?.state
        });

        // Se a câmera está negada mas estamos pedindo vídeo, oferecer fallback para áudio
        if (cameraPermission?.state === 'denied' && targetMode === "video" && !forceFallback) {
          console.log("⚠️ Câmera negada, oferecendo fallback para áudio apenas");
          toast.warning("Permissão de câmera negada. Iniciando chamada apenas com áudio...");
          setCurrentMode("audio");
          setIsFallbackMode(true);
          return await requestMediaPermissions(true); // Tentar novamente só com áudio
        }
        
        if (micPermission?.state === 'denied') {
          throw new Error("Permissão de microfone foi negada. Clique no ícone de microfone na barra de endereços e permita o acesso.");
        }
      } catch (permError) {
        console.log("⚠️ Erro ao verificar permissões existentes:", permError);
        // Continuar mesmo se não conseguir verificar permissões
      }

      const constraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        },
        video: targetMode === "video" ? {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        } : false
      };

      console.log("📋 Solicitando permissões com constraints:", constraints);

      // Mostrar toast informativo antes de solicitar permissões
      toast.info(`Solicitando permissão de ${targetMode === "video" ? "câmera e microfone" : "microfone"}...`);

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      console.log("✅ Permissões de mídia concedidas", {
        audioTracks: stream.getAudioTracks().length,
        videoTracks: stream.getVideoTracks().length,
        audioTrackSettings: stream.getAudioTracks()[0]?.getSettings(),
        videoTrackSettings: stream.getVideoTracks()[0]?.getSettings()
      });

      // Parar o stream imediatamente - só precisamos das permissões
      stream.getTracks().forEach(track => {
        console.log("🛑 Parando track:", track.kind, track.label, track.getSettings());
        track.stop();
      });

      toast.success(forceFallback ? "Chamada de áudio iniciada!" : "Permissões de mídia concedidas!");
      setPermissionState('granted');
      return true;
    } catch (error: any) {
      console.error("❌ Erro ao solicitar permissões de mídia:", error);

      // Se falhar com vídeo e não estamos em fallback, tentar só áudio
      if (error.name === 'NotAllowedError' && targetMode === "video" && !forceFallback) {
        console.log("⚠️ Permissão de vídeo negada, tentando fallback para áudio");
        toast.warning("Permissão de câmera negada. Iniciando chamada apenas com áudio...");
        setCurrentMode("audio");
        setIsFallbackMode(true);
        return await requestMediaPermissions(true);
      }

      let errorMessage = "Erro ao acessar dispositivos de mídia.";

      if (error.name === 'NotAllowedError') {
        errorMessage = "Permissão de microfone/câmera negada. Clique no ícone de câmera/microfone na barra de endereços do navegador e permita o acesso, depois tente novamente.";
        toast.error("Permissão de mídia negada. Verifique as configurações do navegador.");
      } else if (error.name === 'NotFoundError') {
        // Se dispositivo não encontrado e estamos em modo vídeo, tentar fallback para áudio
        if (targetMode === "video" && !forceFallback) {
          console.log("⚠️ Câmera não encontrada, tentando fallback para áudio");
          toast.warning("Câmera não encontrada. Iniciando chamada apenas com áudio...");
          setCurrentMode("audio");
          setIsFallbackMode(true);
          return await requestMediaPermissions(true);
        }
        errorMessage = "Dispositivo de microfone/câmera não encontrado. Verifique se os dispositivos estão conectados e funcionando.";
        toast.error("Dispositivo de mídia não encontrado.");
      } else if (error.name === 'NotReadableError') {
        errorMessage = "Dispositivo de mídia está sendo usado por outro aplicativo. Feche outros aplicativos que possam estar usando a câmera/microfone e tente novamente.";
        toast.error("Dispositivo de mídia em uso por outro aplicativo.");
      } else if (error.name === 'OverconstrainedError') {
        // Tentar com configurações mais básicas
        console.log("⚠️ Tentando com configurações básicas...");
        try {
          const basicConstraints = {
            audio: true,
            video: targetMode === "video" ? true : false
          };
          const basicStream = await navigator.mediaDevices.getUserMedia(basicConstraints);
          basicStream.getTracks().forEach(track => track.stop());
          console.log("✅ Permissões concedidas com configurações básicas");
          toast.success("Permissões concedidas!");
          setPermissionState('granted');
          return true;
        } catch (basicError: any) {
          console.error("❌ Erro mesmo com configurações básicas:", basicError);
          // Se ainda falhar com vídeo, tentar só áudio
          if (targetMode === "video" && !forceFallback) {
            console.log("⚠️ Configurações básicas falharam, tentando fallback para áudio");
            toast.warning("Câmera com problemas. Iniciando chamada apenas com áudio...");
            setCurrentMode("audio");
            setIsFallbackMode(true);
            return await requestMediaPermissions(true);
          }
          errorMessage = "Não foi possível acessar os dispositivos de mídia.";
          toast.error("Não foi possível acessar dispositivos de mídia.");
        }
      } else if (error.name === 'SecurityError') {
        errorMessage = "Erro de segurança ao acessar mídia. Certifique-se de que está usando HTTPS ou localhost.";
        toast.error("Erro de segurança. Use HTTPS ou localhost.");
      } else if (error.name === 'TypeError') {
        errorMessage = "Navegador não suporta acesso à mídia. Use um navegador moderno.";
        toast.error("Navegador não suporta acesso à mídia.");
      }

      setPermissionState('denied');
      setPermissionError(errorMessage);
      return false;
    }
  }, [currentMode]);

  // Conectar à sala (sem solicitar permissões automaticamente)
  const connectToRoom = useCallback(async (isReconnect = false) => {
    if (isConnecting) return;

    try {
      setIsConnecting(true);
      setConnectionError(null);

      // Timeout de conexão (aumentado para 30 segundos)
      connectionTimeoutRef.current = setTimeout(() => {
        console.log("⏰ Timeout de conexão atingido (30s)");
        setIsConnecting(false);
        setConnectionError(new Error("Tempo limite de conexão excedido"));
        onError?.(new Error("Connection timeout"));
      }, 30000);

      console.log(`🔄 ${isReconnect ? 'Reconectando' : 'Conectando'} à sala ${mode}...`);

      const { token: newToken, serverUrl: newServerUrl } = await getLiveKitToken();

      console.log("🎫 Token obtido:", {
        hasToken: !!newToken,
        tokenLength: newToken?.length,
        serverUrl: newServerUrl,
        mode
      });

      setToken(newToken);
      setServerUrl(newServerUrl);

      if (isReconnect) {
        setReconnectAttempts(0);
      } else {
        callStartTimeRef.current = new Date();

        // Iniciar contador de duração
        durationIntervalRef.current = setInterval(() => {
          if (callStartTimeRef.current) {
            const now = new Date();
            const duration = Math.floor((now.getTime() - callStartTimeRef.current.getTime()) / 1000);
            setCallDuration(duration);
          }
        }, 1000);
      }

    } catch (error) {
      console.error("Erro ao conectar:", error);
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido ao conectar";
      setConnectionError(new Error(errorMessage));
      onError?.(error as Error);
    } finally {
      setIsConnecting(false);
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
        connectionTimeoutRef.current = null;
      }
    }
  }, [isConnecting, getLiveKitToken, mode, onError]);

  // Tentar reconectar
  const handleReconnect = useCallback(() => {
    if (reconnectAttempts >= maxReconnectAttempts) {
      toast.error("Número máximo de tentativas excedido");
      return;
    }

    setReconnectAttempts(prev => prev + 1);
    connectToRoom(true);
  }, [connectToRoom, reconnectAttempts]);

  // Encerrar chamada
  const handleEndCall = useCallback(() => {
    setToken(null);
    setServerUrl(null);
    setIsConnected(false);

    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }

    callStartTimeRef.current = null;
    setCallDuration(0);

    toast.info("Chamada encerrada");
    onClose();
  }, [onClose]);

  // Cleanup quando fechar
  useEffect(() => {
    if (!isOpen) {
      setToken(null);
      setServerUrl(null);
      setIsConnected(false);
      setConnectionError(null);
      setReconnectAttempts(0);
      setCallDuration(0);
      setPermissionState('idle');
      setPermissionError(null);

      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
        durationIntervalRef.current = null;
      }

      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
        connectionTimeoutRef.current = null;
      }

      callStartTimeRef.current = null;
    }
  }, [isOpen]);

  // Formatar duração da chamada
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Componente de controles customizados
  const CallControls = () => {
    const { localParticipant } = useLocalParticipant();
    const [isMuted, setIsMuted] = useState(false);
    const [isVideoOff, setIsVideoOff] = useState(mode === "audio");

    const toggleMute = useCallback(async () => {
      if (localParticipant) {
        try {
          const enabled = localParticipant.isMicrophoneEnabled;
          await localParticipant.setMicrophoneEnabled(!enabled);
          setIsMuted(enabled);
          toast.success(enabled ? "Microfone desativado" : "Microfone ativado");
        } catch (error) {
          toast.error("Erro ao alterar microfone");
        }
      }
    }, [localParticipant]);

    const toggleVideo = useCallback(async () => {
      if (localParticipant && mode === "video") {
        try {
          const enabled = localParticipant.isCameraEnabled;
          await localParticipant.setCameraEnabled(!enabled);
          setIsVideoOff(enabled);
          toast.success(enabled ? "Câmera desativada" : "Câmera ativada");
        } catch (error) {
          toast.error("Erro ao alterar câmera");
        }
      }
    }, [localParticipant, mode]);

    return (
      <div className="flex items-center justify-center gap-4 p-6 bg-slate-900/95 backdrop-blur-sm border-t border-slate-700/30">
        {/* Botão de microfone */}
        <Button
          size="lg"
          variant={isMuted ? "error" : "secondary"}
          onClick={toggleMute}
          className={`rounded-full h-14 w-14 audio-call-button ${
            isMuted 
              ? "bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/30" 
              : "bg-slate-700 hover:bg-slate-600 shadow-lg shadow-slate-500/20"
          }`}
          title={isMuted ? "Ativar microfone" : "Desativar microfone"}
        >
          {isMuted ? (
            <MicOff className="h-6 w-6" />
          ) : (
            <Mic className="h-6 w-6" />
          )}
        </Button>

        {/* Botão de câmera (apenas para vídeo) */}
        {currentMode === "video" && (
          <Button
            size="lg"
            variant={isVideoOff ? "error" : "secondary"}
            onClick={toggleVideo}
            className={`rounded-full h-14 w-14 audio-call-button ${
              isVideoOff 
                ? "bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/30" 
                : "bg-slate-700 hover:bg-slate-600 shadow-lg shadow-slate-500/20"
            }`}
            title={isVideoOff ? "Ativar câmera" : "Desativar câmera"}
          >
            {isVideoOff ? (
              <VideoOff className="h-6 w-6" />
            ) : (
              <Video className="h-6 w-6" />
            )}
          </Button>
        )}

        {/* Botão de encerrar chamada */}
        <Button
          size="lg"
          variant="error"
          onClick={handleEndCall}
          className="rounded-full h-16 w-16 audio-call-button bg-red-500 hover:bg-red-600 shadow-xl shadow-red-500/40"
          title="Encerrar chamada"
        >
          <PhoneOff className="h-7 w-7" />
        </Button>
      </div>
    );
  };

  // Componente de status da conexão
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2 text-sm">
      <div className="flex items-center gap-1">
        <Signal className="h-3 w-3" />
        <span className="text-xs">Conexão</span>
      </div>
      <Badge variant={connectionQuality === 'excellent' ? 'default' : connectionQuality === 'good' ? 'secondary' : 'default'} className={connectionQuality === 'poor' ? 'bg-red-500' : ''}>
        {connectionQuality === 'excellent' ? 'Excelente' : connectionQuality === 'good' ? 'Boa' : 'Ruim'}
      </Badge>
    </div>
  );

  // Componente de skeleton loading elegante
  const LoadingSkeleton = () => (
    <div className="h-full flex flex-col">
      {/* Header skeleton */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-center gap-4">
          <Skeleton className="h-12 w-12 rounded-lg" />
          <div className="flex-1">
            <Skeleton className="h-6 w-48 mb-2" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </div>

      {/* Main content skeleton */}
      <div className="flex-1 p-6">
        <div className="h-full flex flex-col items-center justify-center">
          {/* Avatar skeleton */}
          <div className="relative mb-8">
            <Skeleton className="h-32 w-32 rounded-full" />
            <div className="absolute -bottom-2 -right-2">
              <Skeleton className="h-8 w-8 rounded-full" />
            </div>
          </div>

          {/* Text skeletons */}
          <Skeleton className="h-8 w-64 mb-4" />
          <Skeleton className="h-6 w-48 mb-6" />
          
          {/* Status skeleton */}
          <div className="flex items-center gap-3 mb-8">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-4 w-24" />
          </div>

          {/* Controls skeleton */}
          <div className="flex gap-4">
            <Skeleton className="h-14 w-14 rounded-full" />
            {mode === "video" && <Skeleton className="h-14 w-14 rounded-full" />}
            <Skeleton className="h-16 w-16 rounded-full" />
          </div>
        </div>
      </div>
    </div>
  );

  // Componente de preparação da chamada com permissões integradas
  const CallPreparation = () => (
    <div className="h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="text-center text-white max-w-md mx-auto px-6">
        {/* Ícone animado */}
        <div className="relative mb-8">
          <div className="w-24 h-24 mx-auto relative">
            {permissionState === 'granted' ? (
              <div className="absolute inset-0 bg-green-500/20 rounded-full animate-pulse" />
            ) : mode === "video" ? (
              <div className="absolute inset-0 bg-blue-500/20 rounded-full animate-pulse" />
            ) : (
              <div className="absolute inset-0 bg-green-500/20 rounded-full animate-pulse" />
            )}
            <div className="absolute inset-2 bg-gray-800 rounded-full flex items-center justify-center">
              {permissionState === 'granted' ? (
                <CheckCircle className="h-8 w-8 text-green-400" />
              ) : permissionState === 'requesting' ? (
                <RefreshCw className="h-8 w-8 text-blue-400 animate-spin" />
              ) : permissionState === 'denied' ? (
                <XCircle className="h-8 w-8 text-red-400" />
              ) : mode === "video" ? (
                <Video className="h-8 w-8 text-blue-400" />
              ) : (
                <Phone className="h-8 w-8 text-green-400" />
              )}
            </div>
          </div>

          {/* Anel de progresso */}
          {(isConnecting || permissionState === 'requesting') && (
            <div className="absolute inset-0 w-24 h-24 mx-auto">
              <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  className="text-gray-700"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="283"
                  strokeDashoffset="70"
                  className={`${mode === "video" ? "text-blue-400" : "text-green-400"} transition-all duration-1000 ease-in-out`}
                  style={{
                    strokeDasharray: "283",
                    strokeDashoffset: "70",
                    animation: "spin 2s linear infinite"
                  }}
                />
              </svg>
            </div>
          )}
        </div>

        {/* Texto de status baseado no estado das permissões */}
        <h3 className="text-xl font-semibold mb-3">
          {permissionState === 'requesting' ? (
            `Solicitando permissões de ${mode === "video" ? "câmera e microfone" : "microfone"}...`
          ) : permissionState === 'granted' ? (
            isConnecting ? `Conectando à chamada de ${mode}...` : "Permissões concedidas!"
          ) : permissionState === 'denied' ? (
            "Permissões de mídia necessárias"
          ) : (
            `Preparar chamada de ${mode}`
          )}
        </h3>

        <p className="text-gray-400 mb-6">
          {permissionState === 'requesting' ? (
            "O navegador deve mostrar um popup solicitando permissão"
          ) : permissionState === 'granted' ? (
            isConnecting ? "Estabelecendo conexão segura" : "Clique em 'Iniciar Chamada' para conectar"
          ) : permissionState === 'denied' ? (
            "Clique em 'Tentar Novamente' para solicitar permissões"
          ) : (
            `Para iniciar a ${mode === "video" ? "videochamada" : "chamada de áudio"}, primeiro precisamos solicitar permissões de mídia`
          )}
        </p>

        {/* Erro de permissões */}
        {permissionState === 'denied' && permissionError && (
          <Alert className="mb-6 bg-red-500/10 border-red-500/30">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-200 text-sm">
              {permissionError}
            </AlertDescription>
          </Alert>
        )}

        {/* Indicador de progresso */}
        {(isConnecting || permissionState === 'requesting') && (
          <div className="flex items-center justify-center gap-2 mb-6">
            <div className="flex gap-1">
              {[0, 1, 2].map((i) => (
                <div
                  key={i}
                  className={`w-2 h-2 rounded-full ${
                    mode === "video" ? "bg-blue-400" : "bg-green-400"
                  } animate-pulse`}
                  style={{ animationDelay: `${i * 0.2}s` }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Botões de ação */}
        <div className="space-y-3 mb-6">
          {permissionState === 'idle' && (
            <Button
              onClick={() => requestMediaPermissions()}
              disabled={permissionState === 'requesting'}
              variant="outline"
              size="lg"
              className="w-full border-blue-500/50 text-blue-400 hover:bg-blue-500/10 text-lg py-3"
            >
              <Settings className="mr-2 h-5 w-5" />
              Solicitar Permissões de Mídia
            </Button>
          )}

          {permissionState === 'denied' && (
            <Button
              onClick={() => requestMediaPermissions()}
              disabled={permissionState === 'requesting'}
              variant="outline"
              size="lg"
              className="w-full border-blue-500/50 text-blue-400 hover:bg-blue-500/10 text-lg py-3"
            >
              <RefreshCw className="mr-2 h-5 w-5" />
              Tentar Novamente
            </Button>
          )}

          {permissionState === 'granted' && !isConnecting && (
            <Button
              onClick={() => connectToRoom()}
              size="lg"
              className="w-full bg-green-600 hover:bg-green-700 text-lg py-3"
            >
              <Phone className="mr-2 h-5 w-5" />
              Iniciar Chamada
            </Button>
          )}
        </div>

        {/* Informações adicionais */}
        <div className="text-sm text-gray-500">
          <p>Conectando com {otherParticipantName}</p>
        </div>
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleEndCall()}>
      <DialogContent className="max-w-5xl h-[90vh] p-0 overflow-hidden bg-gray-900 transition-all duration-300 ease-in-out">
        <DialogHeader className="p-4 border-b border-slate-700/50 bg-slate-900">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {mode === "video" ? (
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <Video className="h-5 w-5 text-blue-400" />
                </div>
              ) : (
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <Phone className="h-5 w-5 text-green-400" />
                </div>
              )}
              <div>
                <DialogTitle className="text-lg font-semibold text-white">
                  {mode === "video" ? "Videochamada" : "Chamada de áudio"}
                </DialogTitle>
                <div className="flex items-center gap-3 mt-1">
                  <span className="text-sm text-slate-300">{otherParticipantName}</span>
                  {isConnected && (
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                      <span className="text-xs text-green-400">Conectado</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {/* Timer no header */}
            {isConnected && callDuration > 0 && (
              <div className="flex items-center gap-2 text-slate-300">
                <Clock className="h-4 w-4" />
                <span className="font-mono font-semibold">{formatDuration(callDuration)}</span>
              </div>
            )}
            
            {/* Botão de fechar */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEndCall}
              className="text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-full p-2"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 relative bg-gray-900 transition-all duration-300">
          {connectionError ? (
            <div className="h-full flex items-center justify-center p-6 overflow-y-auto">
              {connectionError.message.includes("Permissão") ? (
                <MediaPermissionGuide
                  onTestPermissions={requestMediaPermissions}
                  onRetry={handleReconnect}
                  isTesting={isConnecting}
                  mode={mode}
                />
              ) : (
                <Card className="max-w-md w-full bg-gradient-to-br from-gray-800 to-gray-900 border-gray-700 shadow-2xl">
                  <CardContent className="flex flex-col items-center justify-center p-8">
                    <div className="relative mb-6">
                      <div className="p-4 bg-red-500/20 rounded-full ring-4 ring-red-500/10">
                        <AlertCircle className="h-12 w-12 text-red-400" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse" />
                    </div>
                    
                    <h3 className="text-xl font-semibold mb-2 text-white">Erro de conexão</h3>
                    <p className="text-gray-400 text-sm mb-6 text-center">
                      Não foi possível estabelecer a conexão com a chamada
                    </p>
                    
                    <Alert className="mb-6 w-full bg-red-500/10 border-red-500/30">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-red-200 text-sm">
                        {connectionError.message}
                      </AlertDescription>
                    </Alert>
                    
                    <div className="flex flex-col gap-3 w-full">
                      <Button
                        onClick={handleReconnect}
                        disabled={reconnectAttempts >= maxReconnectAttempts || isConnecting}
                        className="w-full bg-blue-600 hover:bg-blue-700 transition-all duration-200"
                      >
                        {isConnecting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Conectando...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Tentar novamente ({reconnectAttempts}/{maxReconnectAttempts})
                          </>
                        )}
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={handleEndCall} 
                        className="w-full border-gray-600 text-gray-300 hover:bg-gray-700 transition-all duration-200"
                      >
                        Encerrar chamada
                      </Button>
                    </div>
                    
                    {reconnectAttempts >= maxReconnectAttempts && (
                      <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                        <p className="text-yellow-200 text-xs text-center">
                          Número máximo de tentativas atingido. Verifique sua conexão e tente novamente.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          ) : permissionState !== 'granted' || isConnecting || !token || !serverUrl ? (
            <CallPreparation />
          ) : (
            <div className="h-full relative">
              <LiveKitRoom
                token={token}
                serverUrl={serverUrl}
                connect={true}
                video={mode === "video"}
                audio={true}
                className="h-full"
                style={{
                  "--lk-bg": "transparent",
                  "--lk-control-bg": "var(--background)",
                  "--lk-control-border": "var(--border)",
                  "--lk-text-color": "var(--foreground)",
                  "--lk-video-bg": "var(--muted)",
                } as React.CSSProperties}
                options={{
                  adaptiveStream: true,
                  dynacast: true,
                  publishDefaults: {
                    simulcast: currentMode === "video",
                  },
                  // Configurações adicionais para estabilidade
                  reconnectPolicy: {
                    nextRetryDelayInMs: (context) => {
                      console.log("🔄 Tentativa de reconexão:", context);
                      return Math.min(context.retryCount * 1000, 5000);
                    },
                  },
                  // Configurações de rede
                  websocket: {
                    timeout: 30000, // 30 segundos
                  },
                }}
                onConnected={(room) => {
                  console.log("✅ Conectado à sala LiveKit", { 
                    room: !!room,
                    roomName: room?.name,
                    participantCount: room?.numParticipants,
                    localParticipant: room?.localParticipant?.identity
                  });
                  setIsConnected(true);
                  setConnectionError(null);
                  setReconnectAttempts(0);

                  // Verificar se o room existe antes de usar
                  if (room && typeof room.on === 'function') {
                    // Monitorar qualidade da conexão
                    room.on(RoomEvent.ConnectionQualityChanged, (quality, participant) => {
                      console.log("📊 Qualidade da conexão alterada:", { quality, participant: participant.identity });
                      if (participant.identity === room.localParticipant.identity) {
                        const qualityNum = typeof quality === 'number' ? quality : 0.5;
                        if (qualityNum >= 0.8) setConnectionQuality('excellent');
                        else if (qualityNum >= 0.5) setConnectionQuality('good');
                        else setConnectionQuality('poor');
                      }
                    });

                    // Monitorar eventos de participantes
                    room.on(RoomEvent.ParticipantConnected, (participant) => {
                      console.log("👤 Participante conectado:", participant.identity);
                    });

                    room.on(RoomEvent.ParticipantDisconnected, (participant) => {
                      console.log("👋 Participante desconectado:", participant.identity);
                    });
                  } else {
                    console.warn("⚠️ Room object is invalid or missing 'on' method:", room);
                  }
                }}
                onDisconnected={(reason) => {
                  console.log("❌ Desconectado da sala LiveKit:", {
                    reason,
                    timestamp: new Date().toISOString(),
                    reconnectAttempts,
                    maxReconnectAttempts
                  });
                  setIsConnected(false);

                  // Tratar diferentes tipos de desconexão
                  const reasonStr = String(reason);
                  if (reasonStr === "CLIENT_INITIATED") {
                    console.log("ℹ️ Desconexão iniciada pelo cliente - não tentar reconectar");
                    // Não mostrar erro para desconexão intencional
                  } else if (reasonStr === "SERVER_SHUTDOWN") {
                    console.log("⚠️ Servidor LiveKit desligado");
                    toast.error("Servidor temporariamente indisponível. Tentando reconectar...");
                    if (reconnectAttempts < maxReconnectAttempts) {
                      setTimeout(() => handleReconnect(), 3000);
                    }
                  } else if (reasonStr === "PARTICIPANT_REMOVED") {
                    console.log("⚠️ Participante removido da sala");
                    toast.error("Você foi removido da chamada");
                  } else if (reasonStr === "ROOM_DELETED") {
                    console.log("⚠️ Sala foi deletada");
                    toast.error("A sala da chamada foi encerrada");
                  } else {
                    console.log("⚠️ Desconexão inesperada:", reason);
                    toast.error("Desconectado da chamada. Tentando reconectar...");
                    if (reconnectAttempts < maxReconnectAttempts) {
                      setTimeout(() => handleReconnect(), 2000);
                    }
                  }
                }}
                onError={(error) => {
                  console.error("Erro LiveKit:", error);
                  
                  // Tratar erros específicos
                  if (error.name === 'NotAllowedError') {
                    console.error("Permissão de mídia negada pelo usuário");
                    const errorMsg = "Permissão de microfone/câmera negada. Clique no ícone de câmera/microfone na barra de endereços do navegador e permita o acesso, depois tente novamente.";
                    setConnectionError(new Error(errorMsg));
                    toast.error("Permissão de mídia negada. Verifique as configurações do navegador.");
                  } else if (error.name === 'NotFoundError') {
                    console.error("Dispositivo de mídia não encontrado");
                    const errorMsg = "Dispositivo de microfone/câmera não encontrado. Verifique se os dispositivos estão conectados e funcionando.";
                    setConnectionError(new Error(errorMsg));
                    toast.error("Dispositivo de mídia não encontrado.");
                  } else if (error.name === 'NotReadableError') {
                    console.error("Dispositivo de mídia em uso");
                    const errorMsg = "Dispositivo de mídia está sendo usado por outro aplicativo. Feche outros aplicativos que possam estar usando a câmera/microfone e tente novamente.";
                    setConnectionError(new Error(errorMsg));
                    toast.error("Dispositivo de mídia em uso por outro aplicativo.");
                  } else if (error.name === 'OverconstrainedError') {
                    console.error("Configurações de mídia não suportadas");
                    const errorMsg = "Configurações de mídia não suportadas pelo dispositivo. Tente com configurações mais básicas.";
                    setConnectionError(new Error(errorMsg));
                    toast.error("Configurações de mídia não suportadas.");
                  } else if (error.name === 'SecurityError') {
                    console.error("Erro de segurança ao acessar mídia");
                    const errorMsg = "Erro de segurança ao acessar mídia. Certifique-se de que está usando HTTPS ou localhost.";
                    setConnectionError(new Error(errorMsg));
                    toast.error("Erro de segurança ao acessar mídia.");
                  } else {
                    console.error("Erro desconhecido do LiveKit:", error);
                    setConnectionError(error);
                    toast.error("Erro na conexão de vídeo/áudio.");
                  }
                  
                  onError?.(error);
                }}
              >
                <div className="flex h-full flex-col">
                  <div className="flex-1">
                    {currentMode === "video" ? (
                      <VideoConference />
                    ) : (
                      <MultiParticipantAudioCall
                        participants={participants.length > 0 ? participants : [
                          {
                            id: 'local',
                            name: 'Você',
                            isMuted: false,
                            isSpeaking: false,
                            isLocal: true
                          },
                          {
                            id: 'remote',
                            name: otherParticipantName,
                            isMuted: false,
                            isSpeaking: false,
                            isLocal: false
                          }
                        ]}
                        callDuration={callDuration}
                        isConnected={isConnected}
                        onToggleMute={(participantId) => {
                          // Implementar toggle de mute
                          console.log('Toggle mute for participant:', participantId);
                        }}
                        onEndCall={handleEndCall}
                        formatDuration={formatDuration}
                      />
                    )}
                  </div>
                </div>
              </LiveKitRoom>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
