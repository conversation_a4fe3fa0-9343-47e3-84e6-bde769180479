"use client";

import { useState, useEffect } from "react";
import { Phone, Mic, MicOff, Users, Clock } from "lucide-react";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import "./audio-call-animations.css";

interface Participant {
  id: string;
  name: string;
  isMuted: boolean;
  isSpeaking: boolean;
  isLocal: boolean;
}

interface MultiParticipantAudioCallProps {
  participants: Participant[];
  callDuration: number;
  isConnected: boolean;
  onToggleMute: (participantId: string) => void;
  onEndCall: () => void;
  formatDuration: (seconds: number) => string;
}

export function MultiParticipantAudioCall({
  participants,
  callDuration,
  isConnected,
  onToggleMute,
  onEndCall,
  formatDuration
}: MultiParticipantAudioCallProps) {
  const [speakingParticipant, setSpeakingParticipant] = useState<string | null>(null);

  // Simular detecção de fala (em produção, isso viria do LiveKit)
  useEffect(() => {
    const interval = setInterval(() => {
      const speaking = participants.find(p => p.isSpeaking && !p.isMuted);
      setSpeakingParticipant(speaking?.id || null);
    }, 100);

    return () => clearInterval(interval);
  }, [participants]);

  const localParticipant = participants.find(p => p.isLocal);
  const remoteParticipants = participants.filter(p => !p.isLocal);

  return (
    <div className="flex h-full flex-col bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
      {/* Background sutil */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-green-500/20 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-blue-500/20 rounded-full blur-3xl" />
      </div>

      {/* Área principal */}
      <div className="flex-1 flex items-center justify-center relative z-10 p-6">
        <div className="w-full max-w-4xl">
          {/* Layout para 1 participante remoto */}
          {remoteParticipants.length === 1 && (
            <div className="text-center">
              {/* Avatar principal */}
              <div className="relative mb-6">
                {/* Anéis de pulso */}
                <div className="absolute inset-0 w-36 h-36 mx-auto">
                  <div className="w-full h-full border-2 border-green-400/30 rounded-full audio-call-pulse-ring" />
                </div>
                <div className="absolute inset-2 w-32 h-32 mx-auto">
                  <div className="w-full h-full border-2 border-green-400/40 rounded-full audio-call-pulse-ring" style={{ animationDelay: '0.5s' }} />
                </div>
                
                {/* Avatar principal */}
                <div className={`relative w-28 h-28 rounded-full flex items-center justify-center mx-auto shadow-2xl ring-4 transition-all duration-300 ${
                  speakingParticipant === remoteParticipants[0].id
                    ? "bg-gradient-to-br from-green-400 to-green-500 ring-green-400/40 audio-call-glow"
                    : "bg-gradient-to-br from-green-500 to-green-600 ring-green-400/20"
                }`}>
                  <Phone className="h-10 w-10 text-white" />
                </div>
                
                {/* Indicador de status */}
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center ring-2 ring-slate-900">
                  <div className="w-2 h-2 bg-white rounded-full audio-call-pulse-dot" />
                </div>
              </div>
              
              {/* Nome do participante */}
              <div className="mb-4">
                <h3 className="text-xl font-semibold text-white mb-1">{remoteParticipants[0].name}</h3>
                <p className="text-slate-400 text-sm">Chamada de áudio ativa</p>
              </div>
            </div>
          )}

          {/* Layout para 2-4 participantes remotos */}
          {remoteParticipants.length >= 2 && remoteParticipants.length <= 4 && (
            <div className="grid grid-cols-2 gap-6">
              {remoteParticipants.map((participant, index) => (
                <div key={participant.id} className="text-center">
                  {/* Avatar do participante */}
                  <div className="relative mb-4">
                    {/* Anéis de pulso */}
                    <div className="absolute inset-0 w-24 h-24 mx-auto">
                      <div className="w-full h-full border-2 border-green-400/30 rounded-full audio-call-pulse-ring" />
                    </div>
                    
                    {/* Avatar */}
                    <div className={`relative w-20 h-20 rounded-full flex items-center justify-center mx-auto shadow-xl ring-2 transition-all duration-300 ${
                      speakingParticipant === participant.id
                        ? "bg-gradient-to-br from-green-400 to-green-500 ring-green-400/40 audio-call-glow"
                        : "bg-gradient-to-br from-green-500 to-green-600 ring-green-400/20"
                    }`}>
                      <Phone className="h-6 w-6 text-white" />
                    </div>
                    
                    {/* Indicador de status */}
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center ring-1 ring-slate-900">
                      <div className="w-1.5 h-1.5 bg-white rounded-full audio-call-pulse-dot" />
                    </div>
                  </div>
                  
                  {/* Nome e status */}
                  <div>
                    <h4 className="text-sm font-medium text-white mb-1">{participant.name}</h4>
                    <div className="flex items-center justify-center gap-2">
                      {participant.isMuted && (
                        <Badge variant="destructive" size="sm" className="text-xs">
                          <MicOff className="h-3 w-3 mr-1" />
                          Mudo
                        </Badge>
                      )}
                      {speakingParticipant === participant.id && (
                        <Badge variant="default" size="sm" className="text-xs bg-green-500">
                          Falando
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Layout para 5+ participantes */}
          {remoteParticipants.length > 4 && (
            <div className="text-center">
              {/* Avatar principal com contador */}
              <div className="relative mb-6">
                {/* Anéis de pulso */}
                <div className="absolute inset-0 w-36 h-36 mx-auto">
                  <div className="w-full h-full border-2 border-green-400/30 rounded-full audio-call-pulse-ring" />
                </div>
                
                {/* Avatar principal */}
                <div className="relative w-28 h-28 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto shadow-2xl ring-4 ring-green-400/20">
                  <Users className="h-10 w-10 text-white" />
                </div>
                
                {/* Contador de participantes */}
                <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center ring-2 ring-slate-900">
                  <span className="text-xs font-bold text-white">{remoteParticipants.length}</span>
                </div>
              </div>
              
              {/* Informações da chamada em grupo */}
              <div className="mb-4">
                <h3 className="text-xl font-semibold text-white mb-1">Chamada em grupo</h3>
                <p className="text-slate-400 text-sm">{remoteParticipants.length} participantes</p>
              </div>

              {/* Lista de participantes */}
              <div className="max-h-32 overflow-y-auto space-y-2">
                {remoteParticipants.slice(0, 5).map((participant) => (
                  <div key={participant.id} className="flex items-center justify-between bg-slate-800/50 rounded-lg px-3 py-2">
                    <div className="flex items-center gap-2">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                        speakingParticipant === participant.id
                          ? "bg-green-400"
                          : "bg-slate-600"
                      }`}>
                        <Phone className="h-3 w-3 text-white" />
                      </div>
                      <span className="text-sm text-white">{participant.name}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {participant.isMuted && (
                        <MicOff className="h-3 w-3 text-red-400" />
                      )}
                      {speakingParticipant === participant.id && (
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                      )}
                    </div>
                  </div>
                ))}
                {remoteParticipants.length > 5 && (
                  <div className="text-center text-slate-400 text-xs">
                    +{remoteParticipants.length - 5} outros participantes
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Informações do participante local */}
          {localParticipant && (
            <div className="mt-6 text-center">
              <div className="inline-flex items-center gap-2 bg-slate-800/50 rounded-full px-4 py-2">
                <div className="w-6 h-6 bg-slate-600 rounded-full flex items-center justify-center">
                  <Phone className="h-3 w-3 text-white" />
                </div>
                <span className="text-sm text-slate-300">Você</span>
                {localParticipant.isMuted && (
                  <MicOff className="h-4 w-4 text-red-400" />
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Controles */}
      <div className="flex items-center justify-center gap-4 p-6 bg-slate-900/95 backdrop-blur-sm border-t border-slate-700/30">
        {/* Botão de microfone */}
        <Button
          size="lg"
          variant={localParticipant?.isMuted ? "destructive" : "secondary"}
          onClick={() => localParticipant && onToggleMute(localParticipant.id)}
          className={`rounded-full h-14 w-14 audio-call-button ${
            localParticipant?.isMuted
              ? "bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/30" 
              : "bg-slate-700 hover:bg-slate-600 shadow-lg shadow-slate-500/20"
          }`}
          title={localParticipant?.isMuted ? "Ativar microfone" : "Desativar microfone"}
        >
          {localParticipant?.isMuted ? (
            <MicOff className="h-6 w-6" />
          ) : (
            <Mic className="h-6 w-6" />
          )}
        </Button>

        {/* Botão de encerrar chamada */}
        <Button
          size="lg"
          variant="destructive"
          onClick={onEndCall}
          className="rounded-full h-16 w-16 audio-call-button bg-red-500 hover:bg-red-600 shadow-xl shadow-red-500/40"
          title="Encerrar chamada"
        >
          <Phone className="h-7 w-7" />
        </Button>
      </div>
    </div>
  );
}
