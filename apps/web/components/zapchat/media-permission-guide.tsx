"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Badge } from "@ui/components/badge";
import { 
  Camera, 
  Mic, 
  Shield, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Chrome,
  Globe,
  Settings,
  RefreshCw
} from "lucide-react";
import { useMediaPermissions } from "../../lib/hooks/use-media-permissions";

interface MediaPermissionGuideProps {
  onTestPermissions: () => Promise<boolean>;
  onRetry: () => void;
  isTesting: boolean;
  mode: "video" | "audio";
}

export function MediaPermissionGuide({ 
  onTestPermissions, 
  onRetry, 
  isTesting,
  mode
}: MediaPermissionGuideProps) {
  const {
    camera,
    microphone,
    isSupported,
    isLoading,
    error,
    checkPermissions,
    requestPermissions
  } = useMediaPermissions();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'granted':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'denied':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'prompt':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'granted':
        return 'Permitido';
      case 'denied':
        return 'Negado';
      case 'prompt':
        return 'Solicitar';
      default:
        return 'Desconhecido';
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'granted':
        return 'default' as const;
      case 'denied':
        return 'destructive' as const;
      case 'prompt':
        return 'secondary' as const;
      default:
        return 'outline' as const;
    }
  };

  const handleTestPermissions = async () => {
    const success = await requestPermissions({
      audio: true,
      video: mode === "video"
    });
    
    if (success) {
      await onTestPermissions();
    }
  };

  return (
    <Card className="w-full max-w-2xl bg-gradient-to-br from-gray-800 to-gray-900 border-gray-700">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Shield className="h-6 w-6 text-blue-400" />
          <CardTitle className="text-xl text-white">Configuração de Permissões de Mídia</CardTitle>
        </div>
        <p className="text-gray-400 text-sm">
          Para usar chamadas de vídeo e áudio, é necessário permitir o acesso aos dispositivos de mídia
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Status atual das permissões */}
        <div className="space-y-3">
          <h4 className="text-white font-semibold">Status das Permissões:</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-3 p-3 bg-gray-700/50 rounded-lg">
              <Camera className="h-5 w-5 text-blue-400" />
              <div className="flex-1">
                <p className="text-sm text-gray-300">Câmera</p>
                <div className="flex items-center gap-2">
                  {getStatusIcon(camera)}
                  <Badge variant={getStatusVariant(camera)} size="sm">
                    {getStatusText(camera)}
                  </Badge>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-gray-700/50 rounded-lg">
              <Mic className="h-5 w-5 text-green-400" />
              <div className="flex-1">
                <p className="text-sm text-gray-300">Microfone</p>
                <div className="flex items-center gap-2">
                  {getStatusIcon(microphone)}
                  <Badge variant={getStatusVariant(microphone)} size="sm">
                    {getStatusText(microphone)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Instruções por navegador */}
        <div className="space-y-4">
          <h4 className="text-white font-semibold">Como permitir acesso:</h4>
          
          <div className="grid gap-4">
            {/* Chrome/Edge */}
            <div className="p-4 bg-gray-700/30 rounded-lg border border-gray-600">
              <div className="flex items-center gap-2 mb-3">
                <Chrome className="h-5 w-5 text-blue-500" />
                <span className="text-white font-medium">Chrome / Edge</span>
              </div>
              <ol className="text-gray-300 text-sm space-y-1 list-decimal list-inside">
                <li>Clique no ícone de câmera/microfone na barra de endereços</li>
                <li>Selecione "Permitir" para ambos os dispositivos</li>
                <li>Recarregue a página (F5)</li>
              </ol>
            </div>

            {/* Firefox */}
            <div className="p-4 bg-gray-700/30 rounded-lg border border-gray-600">
              <div className="flex items-center gap-2 mb-3">
                <Globe className="h-5 w-5 text-orange-500" />
                <span className="text-white font-medium">Firefox</span>
              </div>
              <ol className="text-gray-300 text-sm space-y-1 list-decimal list-inside">
                <li>Clique no ícone de escudo na barra de endereços</li>
                <li>Selecione "Permitir" para câmera e microfone</li>
                <li>Recarregue a página (F5)</li>
              </ol>
            </div>

            {/* Safari */}
            <div className="p-4 bg-gray-700/30 rounded-lg border border-gray-600">
              <div className="flex items-center gap-2 mb-3">
                <Globe className="h-5 w-5 text-blue-400" />
                <span className="text-white font-medium">Safari</span>
              </div>
              <ol className="text-gray-300 text-sm space-y-1 list-decimal list-inside">
                <li>Vá em Safari → Preferências → Sites</li>
                <li>Selecione "Câmera" e "Microfone"</li>
                <li>Configure para "Permitir" para este site</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Avisos importantes */}
        <Alert className="bg-yellow-500/10 border-yellow-500/30">
          <AlertCircle className="h-4 w-4 text-yellow-400" />
          <AlertDescription className="text-yellow-200 text-sm">
            <strong>Importante:</strong> Certifique-se de que está usando HTTPS ou localhost. 
            Alguns navegadores bloqueiam acesso à mídia em sites não seguros.
          </AlertDescription>
        </Alert>

        {/* Mostrar erro se houver */}
        {error && (
          <Alert className="bg-red-500/10 border-red-500/30">
            <AlertCircle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-200 text-sm">
              {error.message}
            </AlertDescription>
          </Alert>
        )}

        {/* Botões de ação */}
        <div className="flex flex-col gap-3">
          <Button
            onClick={handleTestPermissions}
            disabled={isTesting || isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            {isTesting || isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Testando Permissões...
              </>
            ) : (
              <>
                <Settings className="mr-2 h-4 w-4" />
                Testar Permissões de Mídia
              </>
            )}
          </Button>
          
          <Button
            onClick={onRetry}
            variant="outline"
            className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Tentar Conectar Novamente
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
