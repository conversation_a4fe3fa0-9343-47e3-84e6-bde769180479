import { NextResponse } from "next/server";
import { AccessToken } from "livekit-server-sdk";

export async function GET() {
  try {
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;

    // Verificar configuração
    const config = {
      hasApiKey: !!apiKey,
      hasApiSecret: !!apiSecret,
      hasServerUrl: !!serverUrl,
      apiKeyLength: apiKey?.length || 0,
      apiSecretLength: apiSecret?.length || 0,
      serverUrl: serverUrl,
    };

    if (!apiKey || !apiSecret || !serverUrl) {
      return NextResponse.json({
        success: false,
        error: "Configuração incompleta",
        config,
        issues: [
          !apiKey && "API Key não configurada",
          !apiSecret && "API Secret não configurada", 
          !serverUrl && "Server URL não configurada"
        ].filter(Boolean)
      }, { status: 400 });
    }

    // Testar geração de token
    try {
      const at = new AccessToken(apiKey, apiSecret, {
        identity: 'debug-user',
        name: 'Debug User',
        metadata: JSON.stringify({
          debug: true,
          timestamp: new Date().toISOString()
        }),
      });

      at.addGrant({
        room: 'debug-room',
        roomJoin: true,
        canPublish: true,
        canSubscribe: true,
        canPublishData: true,
      });

      const token = at.toJwt();

      // Preparar URL do servidor
      let formattedServerUrl = serverUrl;
      if (!formattedServerUrl.startsWith("wss://") && !formattedServerUrl.startsWith("ws://")) {
        formattedServerUrl = `wss://${formattedServerUrl}`;
      }

      return NextResponse.json({
        success: true,
        message: "LiveKit configurado corretamente",
        config: {
          ...config,
          formattedServerUrl,
          tokenGenerated: true,
          tokenLength: token?.length || 0,
          tokenPrefix: token ? token.substring(0, 20) + "..." : null,
        },
        recommendations: [
          "URL do servidor formatada corretamente",
          "Token gerado com sucesso",
          "Configuração parece estar correta"
        ]
      });

    } catch (tokenError) {
      return NextResponse.json({
        success: false,
        error: "Falha ao gerar token",
        config,
        tokenError: tokenError instanceof Error ? tokenError.message : "Erro desconhecido",
        recommendations: [
          "Verificar se as chaves de API estão corretas",
          "Verificar se o servidor LiveKit está acessível",
          "Verificar se as chaves não expiraram"
        ]
      }, { status: 500 });
    }

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: "Erro interno",
      details: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 });
  }
}
