import { NextRequest, NextResponse } from "next/server";
import { AccessToken } from "livekit-server-sdk";

export async function GET(request: NextRequest) {
  const debugLog: any[] = [];
  
  const log = (message: string, data?: any) => {
    const timestamp = new Date().toISOString();
    debugLog.push({ timestamp, message, data });
    console.log(`[DEBUG ${timestamp}] ${message}`, data || '');
  };

  try {
    log("🚀 Iniciando debug do backend LiveKit (sem autenticação)");

    // 1. Verificar variáveis de ambiente
    log("🔧 Verificando variáveis de ambiente...");
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;

    log("🔑 Variáveis de ambiente", {
      hasApiKey: !!apiKey,
      apiKeyPrefix: apiKey ? apiKey.substring(0, 8) + "..." : "missing",
      hasApiSecret: !!apiSecret,
      apiSecretPrefix: apiSecret ? apiSecret.substring(0, 8) + "..." : "missing",
      hasServerUrl: !!serverUrl,
      serverUrl: serverUrl
    });

    if (!apiKey || !apiSecret || !serverUrl) {
      log("❌ Variáveis de ambiente não configuradas");
      return NextResponse.json({
        error: "LiveKit environment variables not configured",
        debugLog
      }, { status: 500 });
    }

    // 2. Criar token de acesso
    log("🎫 Criando token de acesso...");
    const at = new AccessToken(apiKey, apiSecret, {
      identity: "debug-user",
      name: "Debug User",
      metadata: JSON.stringify({
        debug: true,
        timestamp: new Date().toISOString()
      }),
    });

    log("👤 Configurações do token", {
      identity: at.identity,
      name: at.name,
      metadata: at.metadata
    });

    // 3. Configurar permissões
    log("🔐 Configurando permissões...");
    const roomName = "debug-room";
    
    at.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
      canUpdateOwnMetadata: true,
    });

    log("🏠 Configurações da sala", {
      roomName,
      grants: {
        roomJoin: true,
        canPublish: true,
        canSubscribe: true,
        canPublishData: true,
        canUpdateOwnMetadata: true
      }
    });

    // 4. Gerar token JWT
    log("🔨 Gerando token JWT...");
    const token = await at.toJwt();
    
    log("✅ Token JWT gerado", {
      hasToken: !!token,
      tokenType: typeof token,
      tokenLength: token ? token.length : 0,
      tokenPreview: token ? token.substring(0, 50) + "..." : "undefined"
    });

    // 5. Preparar URL do servidor
    log("🌐 Preparando URL do servidor...");
    let formattedServerUrl = serverUrl;
    if (!formattedServerUrl.startsWith("wss://") && !formattedServerUrl.startsWith("ws://")) {
      formattedServerUrl = `wss://${formattedServerUrl}`;
    }

    log("🔗 URL do servidor formatada", {
      original: serverUrl,
      formatted: formattedServerUrl
    });

    // 6. Testar conectividade HTTP
    log("🌍 Testando conectividade HTTP...");
    try {
      const https = require('https');
      const testUrl = formattedServerUrl.replace('wss://', 'https://');
      
      await new Promise((resolve, reject) => {
        const req = https.request(testUrl, { method: 'GET' }, (res: any) => {
          log("📡 Resposta HTTP", {
            statusCode: res.statusCode,
            headers: res.headers
          });
          resolve(res);
        });
        
        req.on('error', (error: any) => {
          log("❌ Erro HTTP", error.message);
          reject(error);
        });
        
        req.setTimeout(5000, () => {
          log("⏰ Timeout HTTP");
          req.destroy();
          reject(new Error('HTTP timeout'));
        });
        
        req.end();
      });
      
      log("✅ Conectividade HTTP OK");
    } catch (httpError) {
      log("⚠️ Erro na conectividade HTTP", httpError);
    }

    // 7. Resposta final
    const response = {
      success: true,
      token,
      serverUrl: formattedServerUrl,
      roomName,
      identity: at.identity,
      debugLog
    };

    log("🎉 Debug do backend finalizado com sucesso", {
      hasToken: !!token,
      serverUrl: formattedServerUrl,
      roomName
    });

    return NextResponse.json(response);

  } catch (error) {
    log("💥 Erro no debug do backend", {
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json({
      error: "Erro interno do servidor",
      debugLog
    }, { status: 500 });
  }
}
