import { NextRequest, NextResponse } from 'next/server';
import { db } from 'database';
import { createApiCaller } from 'api/trpc/caller';
import { getBaseUrl } from 'utils';
import { sendPlantaoPaymentConfirmedNotification } from '../../../../actions/appointments/send-plantao-notifications';
import { sendAppointmentNotifications } from '../../../../actions/checkout/notifications/send-notifications';
import { sendEmail } from 'mail';
import { WhatsAppGroupService } from '../../../../lib/whatsapp-group-service';
import { sendImprovedPlantaoPatientMessage } from '../../../../lib/plantao-whatsapp-messages';
import { logPlantaoConfig } from '../../../../lib/plantao-config';

export async function POST(req: NextRequest) {
  try {
    console.log('[PLANTAO_WEBHOOK] Webhook de pagamento confirmado recebido');

    // Log das configurações para debug
    logPlantaoConfig();

    const body = await req.json();
    const { appointmentId, transactionId, paymentId } = body;

    if (!appointmentId) {
      console.error('[PLANTAO_WEBHOOK] appointmentId não fornecido');
      return NextResponse.json(
        { success: false, message: 'appointmentId é obrigatório' },
        { status: 400 }
      );
    }

    console.log('[PLANTAO_WEBHOOK] Processando pagamento confirmado:', {
      appointmentId,
      transactionId,
      paymentId
    });

    // 1. Buscar o appointment e verificar se é plantão
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        patient: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true
              }
            }
          }
        },
        doctor: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true
              }
            }
          }
        }
      }
    });

    if (!appointment) {
      console.error('[PLANTAO_WEBHOOK] Appointment não encontrado:', appointmentId);
      return NextResponse.json(
        { success: false, message: 'Appointment não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se é um appointment de plantão
    if (!appointment.isOnDuty) {
      console.log('[PLANTAO_WEBHOOK] Appointment não é de plantão, ignorando');
      return NextResponse.json({
        success: true,
        message: 'Appointment não é de plantão, webhook ignorado'
      });
    }

    console.log('[PLANTAO_WEBHOOK] Appointment de plantão confirmado:', {
      appointmentId: appointment.id,
      urgencyLevel: appointment.urgencyLevel,
      patientName: appointment.patient?.user?.name,
      patientPhone: appointment.patient?.user?.phone || 'Não disponível',
      paymentStatus: appointment.paymentStatus
    });

    // 2. Atualizar status do pagamento se necessário
    if (appointment.paymentStatus !== 'PAID') {
      await db.appointment.update({
        where: { id: appointmentId },
        data: { paymentStatus: 'PAID' }
      });
      console.log('[PLANTAO_WEBHOOK] Status do pagamento atualizado para PAID');
    }

    // 3. Enviar notificações unificadas (email + WhatsApp) para paciente e médico
    try {
      // Enviar e-mails e WhatsApp usando o serviço unificado
      if (appointment?.patient && appointment?.doctor) {
        void sendAppointmentNotifications(
          {
            id: appointment.id,
            scheduledAt: appointment.scheduledAt,
            // @ts-expect-error appointmentType pode não existir para plantão
            type: (appointment as any)?.appointmentType,
            isOnDuty: appointment.isOnDuty,
          },
          {
            id: appointment.patient.id,
            user: {
              id: appointment.patient.user.id,
              name: appointment.patient.user.name || 'Paciente',
              email: appointment.patient.user.email || '',
              phone: appointment.patient.user.phone || undefined,
            },
            isNewUser: false,
          },
          {
            id: appointment.doctor.id,
            user: {
              id: appointment.doctor.user.id,
              name: appointment.doctor.user.name || 'Médico',
              email: appointment.doctor.user.email || '',
              phone: appointment.doctor.user.phone || undefined,
            },
          },
          { sendEmail: true, sendWhatsApp: true, useDirectLinks: true }
        );
      }
    } catch (notifError) {
      console.error('[PLANTAO_WEBHOOK] Erro ao enviar notificações unificadas:', notifError);
    }

    // 4. Notificar médicos online usando a API
    try {
      console.log('[PLANTAO_WEBHOOK] Iniciando notificação para médicos online...');
      const apiCaller = await createApiCaller();

      // Verificar se temos as variáveis de ambiente necessárias para a API Evolution
      console.log('[PLANTAO_WEBHOOK] Verificando variáveis de ambiente para WhatsApp:', {
        hasEvolutionApiKey: !!process.env.EVOLUTION_API_KEY,
        hasEvolutionInstance: !!process.env.EVOLUTION_INSTANCE,
        hasEvolutionUrl: !!process.env.EVOLUTION_URL,
        siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://zapvida.com'
      });

      const notificationResult = await apiCaller.onDuty.notifyOnlineDoctors({
        appointmentId: appointment.id,
        urgencyLevel: appointment.urgencyLevel as 'HIGH' | 'MEDIUM' | 'LOW',
        patientName: appointment.patient?.user?.name || 'Paciente'
      });

      console.log('[PLANTAO_WEBHOOK] Resultado da notificação para médicos:', {
        success: notificationResult.success,
        notifiedCount: notificationResult.notifiedCount,
        totalDoctors: notificationResult.totalDoctors,
        message: notificationResult.message,
        details: notificationResult.details || []
      });

      // 4.1. Enviar para grupo WhatsApp de médicos (não bloquear em caso de erro)
      const groupResults: {
        medical: { success: boolean; error: string; messagesCount?: number; successCount?: number; groupId?: string };
        admin: { success: boolean; error: string; messagesCount?: number; successCount?: number; groupId?: string };
      } = {
        medical: { success: false, error: 'Não executado' },
        admin: { success: false, error: 'Não executado' }
      };

      try {
        console.log('[PLANTAO_WEBHOOK] Enviando para grupo WhatsApp de médicos...');
        const whatsappGroupService = new WhatsAppGroupService();

        const medicalGroupResult = await whatsappGroupService.sendToMedicalGroup({
          appointmentId: appointment.id,
          patientName: appointment.patient?.user?.name || 'Paciente',
          urgencyLevel: appointment.urgencyLevel as 'HIGH' | 'MEDIUM' | 'LOW',
          scheduledAt: appointment.scheduledAt
        });

        groupResults.medical = {
          success: medicalGroupResult.success,
          error: medicalGroupResult.error || 'Erro desconhecido',
          messagesCount: medicalGroupResult.messagesCount,
          successCount: medicalGroupResult.successCount,
          groupId: medicalGroupResult.groupId
        };

        console.log('[PLANTAO_WEBHOOK] Resultado do envio para grupo de médicos:', {
          success: medicalGroupResult.success,
          error: medicalGroupResult.error,
          messagesCount: medicalGroupResult.messagesCount,
          successCount: medicalGroupResult.successCount
        });
      } catch (groupError) {
        console.error('[PLANTAO_WEBHOOK] Erro ao enviar para grupo de médicos:', groupError);
        groupResults.medical = {
          success: false,
          error: groupError instanceof Error ? groupError.message : 'Erro desconhecido'
        };
      }

      // 4.2. Enviar para grupo admin (DESATIVADO - comentado)
      /*
      try {
        console.log('[PLANTAO_WEBHOOK] Enviando para grupo admin...');
        const whatsappGroupService = new WhatsAppGroupService();

        const adminGroupResult = await whatsappGroupService.sendToAdminGroup({
          appointmentId: appointment.id,
          patientName: appointment.patient?.user?.name || 'Paciente',
          urgencyLevel: appointment.urgencyLevel as 'HIGH' | 'MEDIUM' | 'LOW',
          scheduledAt: appointment.scheduledAt
        });

        groupResults.admin = adminGroupResult;

        console.log('[PLANTAO_WEBHOOK] Resultado do envio para grupo admin:', {
          success: adminGroupResult.success,
          error: adminGroupResult.error,
          messagesCount: adminGroupResult.messagesCount,
          successCount: adminGroupResult.successCount
        });
      } catch (adminGroupError) {
        console.error('[PLANTAO_WEBHOOK] Erro ao enviar para grupo admin:', adminGroupError);
        groupResults.admin = {
          success: false,
          error: adminGroupError instanceof Error ? adminGroupError.message : 'Erro desconhecido'
        };
      }
      */

      // Grupo admin desativado - apenas log
      groupResults.admin = {
        success: true,
        error: 'Grupo admin desativado',
        messagesCount: 0,
        successCount: 0
      };
      console.log('[PLANTAO_WEBHOOK] Grupo admin desativado - notificação ignorada');

      // 5. Enviar notificação WhatsApp específica para o paciente informando que está na fila
      try {
        console.log('[PLANTAO_WEBHOOK] Iniciando envio de notificação para o paciente...');

        // DEBUG: Log detalhado dos dados do paciente
        console.log('[PLANTAO_WEBHOOK] Dados do paciente para debug:', {
          patientId: appointment.patient?.id,
          userId: appointment.patient?.userId,
          patientName: appointment.patient?.user?.name,
          patientEmail: appointment.patient?.user?.email,
          patientPhone: appointment.patient?.user?.phone,
          phoneType: typeof appointment.patient?.user?.phone,
          phoneLength: appointment.patient?.user?.phone?.length,
          phoneTruthy: !!appointment.patient?.user?.phone
        });

        // Verificar se o paciente tem telefone
        let whatsappResult;

        if (!appointment.patient?.user?.phone) {
          console.warn('[PLANTAO_WEBHOOK] Paciente sem telefone cadastrado:', {
            patientId: appointment.patient?.id,
            patientName: appointment.patient?.user?.name,
            patientEmail: appointment.patient?.user?.email || 'Não disponível'
          });

          whatsappResult = {
            success: false,
            error: "Telefone do paciente não encontrado",
            fallback: "Notificação apenas no sistema"
          };
        } else {
          // DEBUG: Log do telefone antes do envio
          console.log('[PLANTAO_WEBHOOK] Telefone encontrado, preparando para envio:', {
            originalPhone: appointment.patient.user.phone,
            phoneLength: appointment.patient.user.phone.length,
            phoneType: typeof appointment.patient.user.phone
          });

          // Enviar notificação WhatsApp aprimorada para o paciente
          whatsappResult = await sendImprovedPlantaoPatientMessage({
            appointmentId: appointment.id,
            patientName: appointment.patient.user.name || 'Paciente',
            urgencyLevel: appointment.urgencyLevel as 'HIGH' | 'MEDIUM' | 'LOW',
            phone: appointment.patient.user.phone
          });
        }

        console.log('[PLANTAO_WEBHOOK] Resultado do envio de WhatsApp para paciente:', {
          success: whatsappResult.success,
          error: whatsappResult.error || null,
          fallback: whatsappResult.fallback || null,
          data: whatsappResult.data || null
        });

        // Criar notificação no banco para o paciente (sempre, independente do WhatsApp)
        await db.notification.create({
          data: {
            userId: appointment.patient.userId,
            appointmentId: appointment.id,
            type: "APPOINTMENT_CREATED",
            title: "Pagamento confirmado - Plantão médico",
            message: `Seu pagamento foi confirmado e os médicos online estão sendo notificados. Você será atendido em breve.`,
            read: false
          }
        });

        console.log('[PLANTAO_WEBHOOK] Notificação para o paciente criada com sucesso');

        // 6. Gerar magic link para login automático (PIX e cartão)
        try {
          console.log('[PLANTAO_WEBHOOK] Gerando magic link para pagamento confirmado...');

          const baseUrl = getBaseUrl();
          const magicLinkResponse = await fetch(`${baseUrl}/api/auth/magic-link`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              appointmentId: appointment.id,
              type: 'PLANTAO'
            })
          });

          if (magicLinkResponse.ok) {
            const magicLinkData = await magicLinkResponse.json();
            console.log('[PLANTAO_WEBHOOK] Magic link gerado com sucesso:', {
              success: magicLinkData.success,
              hasMagicUrl: !!magicLinkData.magicUrl,
              appointmentId: appointment.id,
              paymentMethod: appointment.paymentMethod
            });
          } else {
            console.error('[PLANTAO_WEBHOOK] Erro ao gerar magic link:', {
              status: magicLinkResponse.status,
              statusText: magicLinkResponse.statusText,
              appointmentId: appointment.id,
              paymentMethod: appointment.paymentMethod
            });
          }
        } catch (magicLinkError) {
          console.error('[PLANTAO_WEBHOOK] Erro ao gerar magic link:', magicLinkError);
        }

      } catch (patientNotificationError) {
        console.error('[PLANTAO_WEBHOOK] Erro ao enviar notificação para paciente:', patientNotificationError);

        // Criar notificação de erro no banco
        try {
          await db.notification.create({
            data: {
              userId: appointment.patient.userId,
              appointmentId: appointment.id,
              type: "APPOINTMENT_CREATED",
              title: "Pagamento confirmado - Plantão médico",
              message: `Seu pagamento foi confirmado e os médicos online estão sendo notificados. Você será atendido em breve.`,
              read: false
            }
          });
        } catch (dbError) {
          console.error('[PLANTAO_WEBHOOK] Erro ao criar notificação no banco:', dbError);
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Pagamento processado e médicos notificados',
        data: {
          appointmentId: appointment.id,
          urgencyLevel: appointment.urgencyLevel,
          doctorsNotified: notificationResult.notifiedCount,
          totalDoctorsOnline: notificationResult.totalDoctors,
          patientNotified: appointment.patient?.user?.phone ? true : false,
          groupNotifications: {
            medical: groupResults.medical,
            admin: groupResults.admin
          }
        }
      });

    } catch (notificationError) {
      console.error('[PLANTAO_WEBHOOK] Erro ao notificar médicos:', {
        error: notificationError instanceof Error
          ? notificationError.message
          : 'Erro desconhecido',
        stack: notificationError instanceof Error
          ? notificationError.stack
          : 'Stack não disponível'
      });

      return NextResponse.json({
        success: false,
        message: 'Pagamento processado mas erro ao notificar médicos',
        error: notificationError instanceof Error ? notificationError.message : 'Erro desconhecido'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('[PLANTAO_WEBHOOK] Erro crítico no webhook:', {
      error: error instanceof Error
        ? error.message
        : 'Erro desconhecido',
      stack: error instanceof Error
        ? error.stack
        : 'Stack não disponível'
    });

    return NextResponse.json({
      success: false,
      message: 'Erro interno no processamento do webhook',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
}

// Método GET para verificar se o endpoint está funcionando
export async function GET() {
  const envStatus = {
    hasEvolutionApiKey: !!process.env.EVOLUTION_API_KEY,
    hasEvolutionInstance: !!process.env.EVOLUTION_INSTANCE,
    hasEvolutionUrl: !!process.env.EVOLUTION_URL,
    siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://zapvida.com'
  };

  return NextResponse.json({
    message: 'Webhook de pagamento de plantão funcionando',
    timestamp: new Date().toISOString(),
    environment: envStatus,
    status: 'active',
    features: {
      adminGroupDisabled: true,
      patientNotification: 'active',
      medicalGroupNotification: 'active',
      debugLogs: 'enabled'
    }
  });
}
