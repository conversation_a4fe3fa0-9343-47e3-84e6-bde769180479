import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { NextRequest, NextResponse } from "next/server";

// GET /api/appointments/[id]/medical-record
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await currentUser();
    if (!user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const appointmentId = params.id;

    // Verify user has access to this appointment
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        doctor: {
          include: {
            user: true
          }
        },
        patient: {
          include: {
            user: true
          }
        }
      }
    });

    if (!appointment) {
      return new NextResponse("Appointment not found", { status: 404 });
    }

    // Check if user is the doctor or patient of this appointment
    const isDoctor = appointment.doctor.user.id === user.id;
    const isPatient = appointment.patient.user.id === user.id;

    if (!isDoctor && !isPatient) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Fetch medical record for this appointment
    const medicalRecord = await db.medicalRecord.findUnique({
      where: { appointmentId }
    });

    if (!medicalRecord) {
      return new NextResponse(null, { status: 204 });
    }

    return NextResponse.json(medicalRecord);
  } catch (error) {
    console.error("Error fetching medical record:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// POST /api/appointments/[id]/medical-record
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await currentUser();
    if (!user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Only doctors can create/update medical records
    if (user.role !== "DOCTOR") {
      return new NextResponse("Forbidden", { status: 403 });
    }

    const appointmentId = params.id;
    const body = await request.json();
    const { main_complaint, diagnosis, conduct, cid_codes, cid_descriptions } = body;

    // Validar campos obrigatórios
    if (!diagnosis || !conduct || !cid_codes || cid_codes.length === 0) {
      return NextResponse.json(
        {
          error: "Os campos diagnóstico, conduta e CID são obrigatórios",
          missing_fields: {
            diagnosis: !diagnosis,
            conduct: !conduct,
            cid_codes: !cid_codes || cid_codes.length === 0
          }
        },
        { status: 400 }
      );
    }

    // Verify appointment exists and user is the doctor
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        doctor: {
          include: {
            user: true
          }
        }
      }
    });

    if (!appointment) {
      return new NextResponse("Appointment not found", { status: 404 });
    }

    // Check if user is the doctor of this appointment
    if (appointment.doctor?.user.id !== user.id) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Check if medical record already exists
    const existingRecord = await db.medicalRecord.findUnique({
      where: { appointmentId }
    });

    let medicalRecord;

    const medicalRecordData = {
      main_complaint,
      diagnosis,
      conduct,
      cid_codes: cid_codes || [],
      cid_descriptions: cid_descriptions || []
    };

    if (existingRecord) {
      // Update existing record
      medicalRecord = await db.medicalRecord.update({
        where: { id: existingRecord.id },
        data: medicalRecordData
      });
    } else {
      // Create new record
      medicalRecord = await db.medicalRecord.create({
        data: {
          appointmentId,
          ...medicalRecordData
        }
      });
    }

    return NextResponse.json(medicalRecord);
  } catch (error) {
    console.error("Error creating/updating medical record:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// DELETE /api/appointments/[id]/medical-record
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await currentUser();
    if (!user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Only doctors can delete medical records
    if (user.role !== "DOCTOR") {
      return new NextResponse("Forbidden", { status: 403 });
    }

    const appointmentId = params.id;

    // Verify appointment exists and user is the doctor
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        doctor: {
          include: {
            user: true
          }
        }
      }
    });

    if (!appointment) {
      return new NextResponse("Appointment not found", { status: 404 });
    }

    // Check if user is the doctor of this appointment
    if (appointment.doctor.user.id !== user.id) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Check if medical record exists
    const medicalRecord = await db.medicalRecord.findUnique({
      where: { appointmentId }
    });

    if (!medicalRecord) {
      return new NextResponse("Medical record not found", { status: 404 });
    }

    // Delete the medical record
    await db.medicalRecord.delete({
      where: { id: medicalRecord.id }
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("Error deleting medical record:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
