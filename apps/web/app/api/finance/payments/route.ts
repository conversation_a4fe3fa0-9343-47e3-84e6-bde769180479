import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';
import { UserRoleSchema } from 'database';

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Verificar se o usuário tem permissão para acessar os pagamentos
    if (user.role !== UserRoleSchema.Values.ADMIN && user.role !== UserRoleSchema.Values.DOCTOR) {
      return NextResponse.json({ error: '<PERSON>sso negado' }, { status: 403 });
    }

    const searchParams = request.nextUrl.searchParams;
    const page = Number(searchParams.get('page') || 1);
    const pageSize = Number(searchParams.get('pageSize') || 10);
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const type = searchParams.get('type') || '';

    const skip = Math.max(0, (page - 1) * pageSize);

    // Construir filtros baseados no tipo de usuário
    let where: any = {};

    if (user.role === UserRoleSchema.Values.DOCTOR) {
      // Para médicos, mostrar apenas transações relacionadas a eles
      const doctor = await db.doctor.findFirst({
        where: { userId: user.id },
        select: { id: true },
      });

      if (!doctor) {
        return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
      }

      where.doctorId = doctor.id;
    }

    // Aplicar filtros adicionais
    if (status && status !== 'all') {
      where.status = status;
    }

    if (type && type !== 'all') {
      if (type === 'SUBSCRIPTION') {
        where.patientSubscriptionId = { not: null };
      } else if (type === 'CONSULTATION') {
        where.appointmentId = { not: null };
      }
    }

    // Buscar transações com paginação
    const [transactions, totalCount] = await Promise.all([
      db.transaction.findMany({
        where,
        include: {
          appointment: {
            include: {
              patient: {
                include: {
                  user: {
                    select: {
                      name: true,
                      email: true,
                    },
                  },
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          patientSubscription: {
            include: {
              patient: {
                include: {
                  user: {
                    select: {
                      name: true,
                      email: true,
                    },
                  },
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: pageSize,
      }),
      db.transaction.count({ where }),
    ]);

    // Filtrar por busca se necessário
    let filteredTransactions = transactions;
    if (search) {
      filteredTransactions = transactions.filter(t => {
        const patientName = t.appointment?.patient?.user?.name || t.patientSubscription?.patient?.user?.name || '';
        const doctorName = t.appointment?.doctor?.user?.name || t.patientSubscription?.doctor?.user?.name || '';
        const description = t.appointment ? 'Consulta' : 'Assinatura';
        
        return (
          patientName.toLowerCase().includes(search.toLowerCase()) ||
          doctorName.toLowerCase().includes(search.toLowerCase()) ||
          description.toLowerCase().includes(search.toLowerCase())
        );
      });
    }

    // Formatar transações para resposta
    const formattedTransactions = filteredTransactions.map(t => ({
      id: t.id,
      amount: Number(t.amount),
      status: t.status,
      paymentMethod: t.paymentMethod,
      description: t.appointment ? 'Consulta' : 'Assinatura Premium',
      patientName: t.appointment?.patient?.user?.name || t.patientSubscription?.patient?.user?.name || 'Paciente',
      doctorName: t.appointment?.doctor?.user?.name || t.patientSubscription?.doctor?.user?.name || 'Dr. Não informado',
      paidAt: t.paidAt?.toISOString(),
      createdAt: t.createdAt.toISOString(),
      type: t.appointment ? 'CONSULTATION' : 'SUBSCRIPTION',
    }));

    // Calcular métricas
    const allTransactions = await db.transaction.findMany({
      where: user.role === UserRoleSchema.Values.DOCTOR ? { doctorId: where.doctorId } : {},
    });

    const totalTransactions = allTransactions.length;
    const paidTransactions = allTransactions.filter(t => t.status === 'PAID').length;
    const pendingTransactions = allTransactions.filter(t => t.status === 'PENDING').length;
    const failedTransactions = allTransactions.filter(t => t.status === 'FAILED').length;
    const totalRevenue = allTransactions
      .filter(t => t.status === 'PAID')
      .reduce((sum, t) => sum + Number(t.amount), 0);
    const successRate = totalTransactions > 0 ? Math.round((paidTransactions / totalTransactions) * 100) : 0;

    const totalPages = Math.max(1, Math.ceil(totalCount / pageSize));

    return NextResponse.json({
      transactions: formattedTransactions,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      metrics: {
        totalTransactions,
        paidTransactions,
        pendingTransactions,
        failedTransactions,
        totalRevenue,
        successRate,
      },
    });

  } catch (error) {
    console.error('Error fetching payments:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
