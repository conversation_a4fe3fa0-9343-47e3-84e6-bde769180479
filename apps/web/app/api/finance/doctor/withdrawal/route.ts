import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Buscar o médico pelo userId
    const doctor = await db.doctor.findFirst({
      where: { userId: user.id },
      select: {
        id: true,
        bankAccount: true,
      },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    // Verificar se o médico tem conta bancária configurada
    if (!doctor.bankAccount) {
      return NextResponse.json({
        error: 'Conta bancária não configurada. Configure sua conta bancária para solicitar saques.',
        code: 'BANK_ACCOUNT_NOT_CONFIGURED'
      }, { status: 400 });
    }

    // Verificar se a conta bancária está aprovada
    const bankAccount = doctor.bankAccount as any;
    if (bankAccount.status !== 'APPROVED') {
      return NextResponse.json({
        error: 'Conta bancária ainda não foi aprovada. Aguarde a verificação para solicitar saques.',
        code: 'BANK_ACCOUNT_NOT_APPROVED',
        status: bankAccount.status
      }, { status: 400 });
    }

    const body = await request.json();
    const { amount, bankAccountId, notes } = body;

    // Validações
    if (!amount || amount < 50) {
      return NextResponse.json({
        error: 'Valor mínimo para saque é R$ 50,00'
      }, { status: 400 });
    }

    if (amount > 10000) {
      return NextResponse.json({
        error: 'Valor máximo para saque é R$ 10.000,00'
      }, { status: 400 });
    }

    if (!bankAccountId) {
      return NextResponse.json({
        error: 'Conta bancária é obrigatória'
      }, { status: 400 });
    }

    // Calcular taxa e valor líquido
    const fee = amount * 0.02; // 2% de taxa
    const netAmount = amount - fee;

    // Calcular saldo disponível real do médico
    const paidTransactions = await db.transaction.findMany({
      where: {
        doctorId: doctor.id,
        status: 'PAID',
      },
      select: {
        doctorAmount: true,
      },
    });

    const totalEarnings = paidTransactions.reduce((sum, t) => sum + Number(t.doctorAmount), 0);
    const availableBalance = totalEarnings * 0.7; // 70% disponível imediatamente

    if (amount > availableBalance) {
      return NextResponse.json({
        error: 'Saldo insuficiente para saque'
      }, { status: 400 });
    }

    // Criar solicitação de saque no banco de dados
    const withdrawalRequest = await db.doctorWithdrawal.create({
      data: {
        doctorId: doctor.id,
        amount: new (await import('@prisma/client')).Prisma.Decimal(amount),
        status: 'PENDING',
        bankAccount: doctor.bankAccount,
        notes,
      },
    });

    console.log('Withdrawal request created:', withdrawalRequest);

    return NextResponse.json({
      success: true,
      withdrawal: {
        id: withdrawalRequest.id,
        doctorId: withdrawalRequest.doctorId,
        amount: Number(withdrawalRequest.amount),
        netAmount,
        fee,
        bankAccountId,
        notes: withdrawalRequest.notes,
        status: withdrawalRequest.status.toLowerCase(),
        requestedAt: withdrawalRequest.requestedAt.toISOString(),
      },
      message: 'Solicitação de saque criada com sucesso',
    });

  } catch (error) {
    console.error('Error processing withdrawal request:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Buscar o médico pelo userId
    const doctor = await db.doctor.findFirst({
      where: { userId: user.id },
      select: { id: true },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    // Buscar solicitações de saque reais do banco de dados
    const withdrawals = await db.doctorWithdrawal.findMany({
      where: { doctorId: doctor.id },
      orderBy: { requestedAt: 'desc' },
    });

    // Formatar saques para resposta
    const formattedWithdrawals = withdrawals.map(w => ({
      id: w.id,
      amount: Number(w.amount),
      netAmount: Number(w.amount) * 0.98, // 2% de taxa
      fee: Number(w.amount) * 0.02,
      status: w.status.toLowerCase(),
      requestedAt: w.requestedAt.toISOString(),
      processedAt: w.processedAt?.toISOString(),
      completedAt: w.status === 'COMPLETED' ? w.processedAt?.toISOString() : undefined,
      bankAccount: w.bankAccount as any,
      notes: w.notes,
      transactionId: `TXN-${w.id.slice(-8)}`,
    }));

    return NextResponse.json({
      withdrawals: formattedWithdrawals,
    });

  } catch (error) {
    console.error('Error fetching withdrawal history:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
