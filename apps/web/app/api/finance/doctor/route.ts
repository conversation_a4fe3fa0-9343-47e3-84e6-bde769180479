import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';
import { PaymentStatusSchema, PaymentMethodSchema, type PaymentStatusType, type PaymentMethodType } from 'database';

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Buscar o médico pelo userId
    const doctor = await db.doctor.findFirst({
      where: { userId: user.id },
      select: { id: true },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    const searchParams = request.nextUrl.searchParams;
    const period = searchParams.get('period') || '30days';
    const status = searchParams.get('status');
    const method = searchParams.get('method');

    // Calcular data de início baseada no período
    const now = new Date();
    let startDate = new Date();

    switch (period) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case '7days':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(now.getDate() - 30);
        break;
      case '3months':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case '6months':
        startDate.setMonth(now.getMonth() - 6);
        break;
      case '1year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Construir filtros
    const where: any = {
      doctorId: doctor.id,
      createdAt: {
        gte: startDate,
        lte: now,
      },
    };

    if (status && status !== 'all') {
      where.status = status as PaymentStatusType;
    }

    if (method && method !== 'all') {
      where.paymentMethod = method as PaymentMethodType;
    }

    // Buscar transações
    const transactions = await db.transaction.findMany({
      where,
      include: {
        appointment: {
          include: {
            patient: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Calcular métricas
    const totalRevenue = transactions
      .filter(t => t.status === 'PAID')
      .reduce((sum, t) => sum + Number(t.doctorAmount), 0);

    const monthlyRevenue = transactions
      .filter(t => {
        const transactionDate = new Date(t.createdAt);
        const currentMonth = new Date();
        return (
          t.status === 'PAID' &&
          transactionDate.getMonth() === currentMonth.getMonth() &&
          transactionDate.getFullYear() === currentMonth.getFullYear()
        );
      })
      .reduce((sum, t) => sum + Number(t.doctorAmount), 0);

    const paidAppointments = transactions.filter(t => t.status === 'PAID').length;
    const totalAppointments = transactions.length;
    const conversionRate = totalAppointments > 0 ? (paidAppointments / totalAppointments) * 100 : 0;
    const averageTicket = paidAppointments > 0 ? totalRevenue / paidAppointments : 0;

    // Dados para gráfico de evolução (últimos 30 dias)
    const chartData = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayStart = new Date(date);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(date);
      dayEnd.setHours(23, 59, 59, 999);

      const dayTransactions = transactions.filter(t => {
        const tDate = new Date(t.createdAt);
        return tDate >= dayStart && tDate <= dayEnd && t.status === 'PAID';
      });

      const dayRevenue = dayTransactions.reduce((sum, t) => sum + Number(t.doctorAmount), 0);
      const dayAppointments = dayTransactions.length;

      chartData.push({
        date: date.toISOString().split('T')[0],
        revenue: dayRevenue,
        appointments: dayAppointments,
      });
    }

    // Distribuição por método de pagamento
    const paymentMethods = ['CREDIT_CARD', 'PIX', 'BOLETO'].map(method => {
      const methodTransactions = transactions.filter(t =>
        t.paymentMethod === method && t.status === 'PAID'
      );
      return {
        method,
        count: methodTransactions.length,
        amount: methodTransactions.reduce((sum, t) => sum + Number(t.doctorAmount), 0),
      };
    }).filter(pm => pm.count > 0);

    // Formatar transações para resposta
    const formattedTransactions = transactions.map(t => ({
      id: t.id,
      amount: Number(t.amount),
      doctorAmount: Number(t.doctorAmount),
      platformFee: Number(t.platformFee),
      status: t.status,
      paymentMethod: t.paymentMethod,
      paidAt: t.paidAt?.toISOString(),
      createdAt: t.createdAt.toISOString(),
      patientName: t.appointment?.patient?.user?.name || 'Paciente',
      appointmentDate: t.appointment?.scheduledAt?.toISOString(),
    }));

    // Calculate balance information
    const availableBalance = totalRevenue * 0.7; // 70% available immediately
    const pendingBalance = totalRevenue * 0.3; // 30% pending processing

    // Buscar dados bancários reais do médico
    const doctorWithBankAccount = await db.doctor.findFirst({
      where: { userId: user.id },
      select: {
        id: true,
        bankAccount: true,
        user: {
          select: {
            name: true,
            email: true,
          }
        }
      },
    });

    // Formatar contas bancárias
    let bankAccounts: any[] = [];
    let bankAccountStatus = 'NOT_CONFIGURED';

    if (doctorWithBankAccount?.bankAccount) {
      const bankAccount = doctorWithBankAccount.bankAccount as any;
      bankAccountStatus = bankAccount.status || 'PENDING_VERIFICATION';

      bankAccounts = [{
        id: 'primary-bank-account',
        bankName: bankAccount.bankName || 'N/A',
        bankCode: bankAccount.bankCode || 'N/A',
        accountType: bankAccount.accountType === 'CHECKING' ? 'Conta Corrente' : 'Conta Poupança',
        accountNumber: bankAccount.accountNumber || 'N/A',
        agency: bankAccount.agency || 'N/A',
        holderName: bankAccount.holderName || 'N/A',
        holderDocument: bankAccount.holderDocument || 'N/A',
        isDefault: true,
        status: bankAccountStatus,
        createdAt: bankAccount.createdAt,
        updatedAt: bankAccount.updatedAt,
      }];
    }

    // Buscar histórico de saques real do banco de dados
    const withdrawals = await db.doctorWithdrawal.findMany({
      where: { doctorId: doctor.id },
      orderBy: { requestedAt: 'desc' },
      take: 10, // Limitar a 10 saques mais recentes
    });

    // Formatar saques para resposta
    const formattedWithdrawals = withdrawals.map(w => ({
      id: w.id,
      amount: Number(w.amount),
      netAmount: Number(w.amount) * 0.98, // 2% de taxa
      fee: Number(w.amount) * 0.02,
      status: w.status.toLowerCase(),
      requestedAt: w.requestedAt.toISOString(),
      processedAt: w.processedAt?.toISOString(),
      completedAt: w.status === 'COMPLETED' ? w.processedAt?.toISOString() : undefined,
      bankAccount: w.bankAccount as any,
      notes: w.notes,
      transactionId: `TXN-${w.id.slice(-8)}`,
    }));

    // Calcular analytics baseado em dados reais
    const currentMonth = new Date();
    const lastMonth = new Date();
    lastMonth.setMonth(currentMonth.getMonth() - 1);

    // Buscar dados do mês anterior para comparação
    const lastMonthTransactions = await db.transaction.findMany({
      where: {
        doctorId: doctor.id,
        status: 'PAID',
        createdAt: {
          gte: lastMonth,
          lt: currentMonth,
        },
      },
    });

    const lastMonthRevenue = lastMonthTransactions.reduce((sum, t) => sum + Number(t.doctorAmount), 0);
    const lastMonthAppointments = lastMonthTransactions.length;
    const lastMonthAverageTicket = lastMonthAppointments > 0 ? lastMonthRevenue / lastMonthAppointments : 0;

    // Calcular crescimento
    const revenueGrowth = lastMonthRevenue > 0 ? ((totalRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;
    const appointmentsGrowth = lastMonthAppointments > 0 ? ((paidAppointments - lastMonthAppointments) / lastMonthAppointments) * 100 : 0;
    const averageTicketGrowth = lastMonthAverageTicket > 0 ? ((averageTicket - lastMonthAverageTicket) / lastMonthAverageTicket) * 100 : 0;

    // Análise por método de pagamento baseada em dados reais
    const paymentMethodAnalysis = paymentMethods.map(pm => {
      const totalMethodRevenue = pm.amount;
      const percentage = totalRevenue > 0 ? (totalMethodRevenue / totalRevenue) * 100 : 0;
      return {
        method: pm.method,
        percentage: Math.round(percentage * 100) / 100,
        revenue: totalMethodRevenue,
        trend: 'stable' as const, // Poderia ser calculado comparando com mês anterior
      };
    });

    const analytics = {
      monthlyGrowth: {
        revenue: Math.round(revenueGrowth * 100) / 100,
        appointments: Math.round(appointmentsGrowth * 100) / 100,
        averageTicket: Math.round(averageTicketGrowth * 100) / 100,
      },
      topPerformingPeriods: chartData
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 3)
        .map(day => ({
          period: new Date(day.date).toLocaleDateString('pt-BR', { weekday: 'long' }),
          revenue: day.revenue,
          appointments: day.appointments,
        })),
      paymentMethodAnalysis,
      revenueGoals: {
        monthly: {
          target: 25000,
          current: totalRevenue,
          percentage: Math.min((totalRevenue / 25000) * 100, 100),
        },
        yearly: {
          target: 300000,
          current: totalRevenue * 12, // Estimate
          percentage: Math.min(((totalRevenue * 12) / 300000) * 100, 100),
        },
      },
      insights: [
        {
          type: revenueGrowth > 0 ? 'positive' as const : revenueGrowth < 0 ? 'negative' as const : 'neutral' as const,
          title: revenueGrowth > 0 ? 'Crescimento Positivo' : revenueGrowth < 0 ? 'Declínio na Receita' : 'Receita Estável',
          description: revenueGrowth > 0 
            ? `Sua receita cresceu ${Math.abs(revenueGrowth).toFixed(1)}% nos últimos 30 dias.`
            : revenueGrowth < 0 
            ? `Sua receita diminuiu ${Math.abs(revenueGrowth).toFixed(1)}% nos últimos 30 dias.`
            : 'Sua receita se manteve estável nos últimos 30 dias.',
          value: revenueGrowth > 0 ? `+R$ ${(totalRevenue - lastMonthRevenue).toFixed(2)}` : undefined,
        },
        {
          type: 'neutral' as const,
          title: 'Método de Pagamento Preferido',
          description: paymentMethodAnalysis.length > 0 
            ? `${paymentMethodAnalysis[0].method} representa ${paymentMethodAnalysis[0].percentage.toFixed(1)}% dos pagamentos.`
            : 'Nenhum pagamento registrado ainda.',
        },
      ],
    };

    return NextResponse.json({
      doctorId: doctor.id, // Para ferramentas de desenvolvimento
      metrics: {
        totalRevenue,
        monthlyRevenue,
        paidAppointments,
        conversionRate: Math.round(conversionRate * 100) / 100,
        averageTicket,
      },
      balance: {
        availableBalance,
        pendingBalance,
        totalEarnings: totalRevenue,
        lastWithdrawal: formattedWithdrawals.length > 0 ? {
          amount: formattedWithdrawals[0].amount,
          date: formattedWithdrawals[0].completedAt || formattedWithdrawals[0].requestedAt,
          status: formattedWithdrawals[0].status as 'completed' | 'pending' | 'processing',
        } : undefined,
        balanceHistory: [
          // Histórico baseado em transações reais (últimas 5)
          ...transactions.slice(0, 5).map(t => ({
            date: t.createdAt.toISOString(),
            amount: Number(t.doctorAmount),
            type: 'earning' as const,
            description: `Consulta - ${t.appointment?.patient?.user?.name || 'Paciente'}`,
          })),
          // Histórico de saques (últimos 3)
          ...formattedWithdrawals.slice(0, 3).map(w => ({
            date: w.requestedAt,
            amount: -w.amount,
            type: 'withdrawal' as const,
            description: 'Saque para conta bancária',
          })),
        ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 10),
      },
      bankAccounts,
      bankAccountStatus, // Status da conta bancária
      withdrawals: formattedWithdrawals,
      analytics,
      chartData,
      paymentMethods,
      transactions: formattedTransactions,
    });

  } catch (error) {
    console.error('Error fetching doctor financial data:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
