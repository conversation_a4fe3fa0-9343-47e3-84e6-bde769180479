// /apps/web/app/[locale]/(saas)/app/plantao/components/plantao-medico-client.tsx
"use client";

import { useEffect, useState } from "react";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { Clock, AlertCircle, AlertTriangle, Info, Search, UserRound, Calendar, Users, Activity } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@ui/components/dialog";
import { PlantaoSkeleton } from "./plantao-skeleton";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON>ialog<PERSON>ooter,
  <PERSON><PERSON><PERSON>ialog<PERSON>eader,
  AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { apiClient } from "@shared/lib/api-client";
import { toast } from "sonner";

// Interface para os pacientes na fila
interface PacienteFila {
  id: string;
  nome: string;
  fotoUrl: string;
  idade: number;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  sintomasResumo: string;
  nivelUrgencia: 'Nível 1 (Emergência)' | 'Nível 2 (Urgência)' | 'Nível 3 (Pouco Urgente)';
  tempoNaFilaMinutes: number;
  motivoCompleto?: string;
  localizacaoAproximada?: string;
  planoSaudeStatus?: 'Verificado' | 'Pagamento Pendente' | 'N/A';
  appointmentId: string;
  queuePosition: number;
  waitTimeMinutes: number;
  urgencyLevel: 'HIGH' | 'MEDIUM' | 'LOW';
  amount: number;
  paymentStatus: string;
}

// Componente principal
export function PlantaoMedicoClient() {
  const [pacientes, setPacientes] = useState<PacienteFila[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusMedico, setStatusMedico] = useState<"Online" | "Em Atendimento">("Online");
  const [pacienteSelecionado, setPacienteSelecionado] = useState<PacienteFila | null>(null);
  const [dialogAberto, setDialogAberto] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cursor, setCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  // Query para buscar a fila de plantão
  const { data: queueData, isLoading, refetch } = apiClient.onDuty.getQueue.useQuery({
    status: "SCHEDULED",
    limit: 10,
  }, {
    refetchInterval: 30000, // Atualizar a cada 30 segundos
  });

  // Mutation para aceitar atendimento
  const acceptAppointmentMutation = apiClient.onDuty.acceptAppointment.useMutation({
    onSuccess: (data) => {
      toast.success("Atendimento aceito com sucesso!");
      refetch(); // Atualizar a fila
      setDialogAberto(false);
      setPacienteSelecionado(null);
      setConfirmationModalOpen(false);
      setPacienteParaAceitar(null);

      // Redirecionar para o chat com o paciente aceito
      if (data?.id) {
        			window.location.href = `/app/zapchat?appointment=${data.id}`;
      } else {
        // Fallback: redirecionar para a página de chats
        			window.location.href = '/app/zapchat';
      }
    },
    onError: (error) => {
      console.error("Erro ao aceitar atendimento:", error);
      toast.error("Erro ao aceitar atendimento: " + error.message);
      setConfirmationModalOpen(false);
      setPacienteParaAceitar(null);
    }
  });

  // Mutation para carregar mais dados
  const loadMoreMutation = apiClient.onDuty.getQueue.useMutation();

  // Função para carregar mais pacientes
  const loadMorePacientes = async () => {
    if (!cursor || !hasMore || loadingMore) return;

    setLoadingMore(true);
    try {
      const moreData = await loadMoreMutation.mutateAsync({
        status: "SCHEDULED",
        limit: 10,
        cursor: cursor,
      });

      if (moreData && moreData.items) {
        const convertedData: PacienteFila[] = moreData.items.map((appointment: any) => {
          const urgencyMap: Record<string, 'Nível 1 (Emergência)' | 'Nível 2 (Urgência)' | 'Nível 3 (Pouco Urgente)'> = {
            'HIGH': 'Nível 1 (Emergência)',
            'MEDIUM': 'Nível 2 (Urgência)',
            'LOW': 'Nível 3 (Pouco Urgente)'
          };

          const patient = appointment.patient;
          const user = patient?.user;

          const idade = patient?.birthDate
            ? Math.floor((Date.now() - new Date(patient.birthDate).getTime()) / (365.25 * 24 * 60 * 60 * 1000))
            : Math.floor(Math.random() * 50) + 20;

          return {
            id: appointment.id,
            appointmentId: appointment.id,
            nome: user?.name || 'Paciente',
            fotoUrl: user?.avatarUrl || `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 99)}.jpg`,
            idade,
            genero: patient?.gender || (Math.random() > 0.5 ? 'Masculino' : 'Feminino'),
            sintomasResumo: appointment.symptoms || 'Consulta de plantão',
            nivelUrgencia: urgencyMap[appointment.urgencyLevel as keyof typeof urgencyMap] || 'Nível 3 (Pouco Urgente)',
            tempoNaFilaMinutes: appointment.waitTimeMinutes || 0,
            motivoCompleto: appointment.symptoms || 'Paciente solicitou atendimento de plantão',
            localizacaoAproximada: 'Brasil',
            planoSaudeStatus: appointment.paymentStatus === 'PAID' ? 'Verificado' : 'Pagamento Pendente',
            queuePosition: appointment.queuePosition || 0,
            waitTimeMinutes: appointment.waitTimeMinutes || 0,
            urgencyLevel: appointment.urgencyLevel || 'LOW',
            amount: Number(appointment.amount) || 0,
            paymentStatus: appointment.paymentStatus || 'PENDING'
          };
        });

        setPacientes(prev => [...prev, ...convertedData]);
        setCursor(moreData.nextCursor || null);
        setHasMore(moreData.hasNextPage || false);
      }
    } catch (error) {
      console.error("Erro ao carregar mais pacientes:", error);
      toast.error("Erro ao carregar mais pacientes");
    } finally {
      setLoadingMore(false);
    }
  };

  // Converter dados da API para o formato esperado pelo componente
  useEffect(() => {
    if (queueData) {
      const convertedData: PacienteFila[] = (queueData.items || []).map((appointment: any, index: number) => {
        // Mapear nível de urgência
        const urgencyMap: Record<string, 'Nível 1 (Emergência)' | 'Nível 2 (Urgência)' | 'Nível 3 (Pouco Urgente)'> = {
          'HIGH': 'Nível 1 (Emergência)',
          'MEDIUM': 'Nível 2 (Urgência)',
          'LOW': 'Nível 3 (Pouco Urgente)'
        };

        // Extrair informações do paciente
        const patient = appointment.patient;
        const user = patient?.user;

        // Calcular idade aproximada (se não tiver data de nascimento)
        const idade = patient?.birthDate
          ? Math.floor((Date.now() - new Date(patient.birthDate).getTime()) / (365.25 * 24 * 60 * 60 * 1000))
          : Math.floor(Math.random() * 50) + 20; // Idade aleatória como fallback

        return {
          id: appointment.id,
          appointmentId: appointment.id,
          nome: user?.name || 'Paciente',
          fotoUrl: user?.avatarUrl || `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 99)}.jpg`,
          idade,
          genero: patient?.gender || (Math.random() > 0.5 ? 'Masculino' : 'Feminino'),
          sintomasResumo: appointment.symptoms || 'Consulta de plantão',
          nivelUrgencia: urgencyMap[appointment.urgencyLevel as keyof typeof urgencyMap] || 'Nível 3 (Pouco Urgente)',
          tempoNaFilaMinutes: appointment.waitTimeMinutes || 0,
          motivoCompleto: appointment.symptoms || 'Paciente solicitou atendimento de plantão',
          localizacaoAproximada: 'Brasil', // Placeholder
          planoSaudeStatus: appointment.paymentStatus === 'PAID' ? 'Verificado' : 'Pagamento Pendente',
          queuePosition: appointment.queuePosition || index + 1,
          waitTimeMinutes: appointment.waitTimeMinutes || 0,
          urgencyLevel: appointment.urgencyLevel || 'LOW',
          amount: Number(appointment.amount) || 0,
          paymentStatus: appointment.paymentStatus || 'PENDING'
        };
      });

      setPacientes(convertedData);
      setCursor(queueData.nextCursor || null);
      setHasMore(queueData.hasNextPage || false);
      setLoading(false);
    }
  }, [queueData]);

  // Filtragem de pacientes baseada no termo de busca
  const pacientesFiltrados = pacientes.filter((paciente) =>
    paciente.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
    paciente.sintomasResumo.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Ordenação por urgência e tempo na fila
  const pacientesOrdenados = [...pacientesFiltrados].sort((a, b) => {
    // Prioridade por nível de urgência
    const urgencyOrder = {
      'Nível 1 (Emergência)': 3,
      'Nível 2 (Urgência)': 2,
      'Nível 3 (Pouco Urgente)': 1
    };
    const urgencyDiff = urgencyOrder[a.nivelUrgencia] - urgencyOrder[b.nivelUrgencia];

    if (urgencyDiff !== 0) return -urgencyDiff;

    // Se mesma urgência, ordena por tempo de espera
    return b.tempoNaFilaMinutes - a.tempoNaFilaMinutes;
  });

  // Estado para modal de confirmação
  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false);
  const [pacienteParaAceitar, setPacienteParaAceitar] = useState<PacienteFila | null>(null);

  // Função para abrir modal de confirmação
  const abrirConfirmacaoAceite = (paciente: PacienteFila) => {
    if (paciente.paymentStatus !== 'PAID') {
      toast.error("Não é possível aceitar atendimento com pagamento pendente");
      return;
    }
    setPacienteParaAceitar(paciente);
    setConfirmationModalOpen(true);
  };

  // Função para confirmar aceite do atendimento
  const confirmarAceiteAtendimento = () => {
    if (!pacienteParaAceitar) return;

    acceptAppointmentMutation.mutate({
      appointmentId: pacienteParaAceitar.appointmentId
    });
  };

  // Função para abrir detalhes do paciente
  const abrirDetalhesPaciente = (paciente: PacienteFila) => {
    setPacienteSelecionado(paciente);
    setDialogAberto(true);
  };

  // Função para formatar tempo na fila
  const formatarTempoFila = (minutos: number) => {
    if (minutos < 60) {
      return `${minutos} MIN`;
    } else {
      const horas = Math.floor(minutos / 60);
      const minutosRestantes = minutos % 60;
      return `${horas}H ${minutosRestantes}MIN`;
    }
  };

  // Função para obter ícone de urgência
  const getUrgencyIcon = (nivel: string) => {
    switch (nivel) {
      case 'Nível 1 (Emergência)':
        return <AlertCircle className="h-4 w-4" />;
      case 'Nível 2 (Urgência)':
        return <AlertTriangle className="h-4 w-4" />;
      case 'Nível 3 (Pouco Urgente)':
        return <Info className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  // Função para obter status do badge de urgência
  const getUrgencyBadgeStatus = (nivel: string): "success" | "info" | "warning" | "error" => {
    switch (nivel) {
      case 'Nível 1 (Emergência)':
        return "error";
      case 'Nível 2 (Urgência)':
        return "warning";
      case 'Nível 3 (Pouco Urgente)':
        return "info";
      default:
        return "info";
    }
  };

  if (loading || isLoading) {
    return <PlantaoSkeleton />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => refetch()} variant="outline">
            Tentar novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header melhorado com informações úteis para médicos */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
          {/* Lado esquerdo - Título e data */}
          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-gray-900">Plantão Médico</h1>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>{new Date().toLocaleDateString('pt-BR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}</span>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>08:00 - 20:00</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>21 médicos online</span>
              </div>
            </div>
          </div>

          {/* Lado direito - Status e busca */}
          <div className="flex flex-col sm:flex-row items-center gap-4 w-full lg:w-auto">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-600">ONLINE</span>
              </div>
              <div className="text-sm text-gray-500">
                Atualizado em tempo real
              </div>
            </div>

            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar paciente ou sintoma..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Estatísticas da fila */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Card 1: Pacientes na Fila */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Pacientes na Fila</p>
                <p className="text-2xl font-bold text-blue-600">{pacientes.length}</p>
                <p className="text-xs text-gray-500">0 na última hora</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full" 
                    style={{ width: `${Math.min((pacientes.length / 50) * 100, 100)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500">Capacidade: {Math.min((pacientes.length / 50) * 100, 100).toFixed(0)}%</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Card 2: Casos Urgentes */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Casos Urgentes</p>
                <p className="text-2xl font-bold text-red-600">
                  {pacientes.filter(p => p.nivelUrgencia === 'Nível 1 (Emergência)').length}
                </p>
                <p className="text-xs text-gray-500">
                  {pacientes.filter(p => p.nivelUrgencia === 'Nível 1 (Emergência)').length === 0 
                    ? 'Nenhum caso crítico' 
                    : 'Atenção necessária'}
                </p>
              </div>
              <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Card 3: Tempo Médio */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Tempo Médio</p>
                <p className="text-2xl font-bold text-orange-600">
                  {pacientes.length > 0 
                    ? `${Math.round(pacientes.reduce((acc, p) => acc + p.tempoNaFilaMinutes, 0) / pacientes.length)}min`
                    : '0min'
                  }
                </p>
                <p className="text-xs text-gray-500">Tempo médio na fila</p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Card 4: Seus Atendimentos */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Seus Atendimentos</p>
                <p className="text-2xl font-bold text-green-600">0</p>
                <p className="text-xs text-gray-500">Em andamento</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                <Activity className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs e Lista de pacientes */}
      <div className="bg-white rounded-lg border">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button className="py-4 px-1 border-b-2 border-blue-500 text-blue-600 font-medium text-sm">
              Pacientes na Fila ({pacientesOrdenados.length})
            </button>
            <button className="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
              Meus Pacientes
            </button>
          </nav>
        </div>

        {/* Conteúdo da lista */}
        <div className="p-6">
          {pacientesOrdenados.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Info className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum paciente na fila</h3>
                <p className="text-gray-500">
                  {searchTerm ? "Nenhum resultado encontrado para sua busca" : "No momento não há pacientes aguardando atendimento de plantão."}
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {pacientesOrdenados.map((paciente) => (
                <Card key={paciente.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={paciente.fotoUrl} alt={paciente.nome} />
                      <AvatarFallback>
                        {paciente.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold text-lg">{paciente.nome}</h3>
                        <Badge
                          status={getUrgencyBadgeStatus(paciente.nivelUrgencia)}
                          className="flex items-center gap-1"
                        >
                          {getUrgencyIcon(paciente.nivelUrgencia)}
                          {paciente.nivelUrgencia}
                        </Badge>
                      </div>

                      <div className="text-sm text-gray-600 mb-2">
                        {paciente.idade} anos • {paciente.genero} • {paciente.localizacaoAproximada}
                      </div>

                      <p className="text-gray-800 mb-2">{paciente.sintomasResumo}</p>

                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-gray-500" />
                          <span className="text-gray-600">
                            {formatarTempoFila(paciente.tempoNaFilaMinutes)}
                          </span>
                        </div>

                        <div className={cn(
                          "px-2 py-1 rounded-full text-xs font-medium",
                          paciente.planoSaudeStatus === 'Verificado'
                            ? "bg-green-100 text-green-800"
                            : paciente.planoSaudeStatus === 'Pagamento Pendente'
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-gray-100 text-gray-800"
                        )}>
                          {paciente.planoSaudeStatus}
                        </div>

                        <div className="text-gray-600">
                          R$ {paciente.amount.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => abrirDetalhesPaciente(paciente)}
                    >
                      Ver detalhes
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => abrirConfirmacaoAceite(paciente)}
                      disabled={paciente.paymentStatus !== 'PAID' || acceptAppointmentMutation.isPending}
                      className={cn(
                        paciente.paymentStatus !== 'PAID' && "opacity-50 cursor-not-allowed"
                      )}
                    >
                      {acceptAppointmentMutation.isPending ? "Aceitando..." : "Aceitar atendimento"}
                    </Button>
                  </div>
                </div>
              </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Botão Carregar Mais */}
          {hasMore && pacientesOrdenados.length > 0 && (
            <div className="flex justify-center pt-4">
              <Button
                variant="outline"
                size="lg"
                onClick={loadMorePacientes}
                disabled={loadingMore}
                className="min-w-[200px]"
              >
                {loadingMore ? (
                  <span className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                    Carregando...
                  </span>
                ) : (
                  "Carregar mais pacientes"
                )}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Dialog de detalhes do paciente */}
      <Dialog open={dialogAberto} onOpenChange={setDialogAberto}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detalhes do Paciente</DialogTitle>
            <DialogDescription>
              Informações completas sobre o atendimento solicitado
            </DialogDescription>
          </DialogHeader>

          {pacienteSelecionado && (
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={pacienteSelecionado.fotoUrl} alt={pacienteSelecionado.nome} />
                  <AvatarFallback>
                    {pacienteSelecionado.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-xl font-semibold">{pacienteSelecionado.nome}</h3>
                  <p className="text-gray-600">
                    {pacienteSelecionado.idade} anos • {pacienteSelecionado.genero}
                  </p>
                  <Badge
                    status={getUrgencyBadgeStatus(pacienteSelecionado.nivelUrgencia)}
                    className="flex items-center gap-1 w-fit mt-2"
                  >
                    {getUrgencyIcon(pacienteSelecionado.nivelUrgencia)}
                    {pacienteSelecionado.nivelUrgencia}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Tempo na fila</h4>
                  <p className="text-gray-600">{formatarTempoFila(pacienteSelecionado.tempoNaFilaMinutes)}</p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Localização</h4>
                  <p className="text-gray-600">{pacienteSelecionado.localizacaoAproximada}</p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Status do pagamento</h4>
                  <p className="text-gray-600">{pacienteSelecionado.planoSaudeStatus}</p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Valor da consulta</h4>
                  <p className="text-gray-600">R$ {pacienteSelecionado.amount.toFixed(2)}</p>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Motivo da consulta</h4>
                <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                  {pacienteSelecionado.motivoCompleto}
                </p>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogAberto(false)}>
              Fechar
            </Button>
            {pacienteSelecionado && (
              <Button
                onClick={() => abrirConfirmacaoAceite(pacienteSelecionado)}
                disabled={pacienteSelecionado.paymentStatus !== 'PAID' || acceptAppointmentMutation.isPending}
              >
                {acceptAppointmentMutation.isPending ? "Aceitando..." : "Aceitar atendimento"}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de confirmação para aceitar atendimento */}
      <AlertDialog open={confirmationModalOpen} onOpenChange={setConfirmationModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Aceitar Atendimento de Plantão</AlertDialogTitle>
            <AlertDialogDescription>
              Você está prestes a aceitar o atendimento do paciente{' '}
              <strong>{pacienteParaAceitar?.nome}</strong>.
            </AlertDialogDescription>
          </AlertDialogHeader>

          {pacienteParaAceitar && (
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={pacienteParaAceitar.fotoUrl} alt={pacienteParaAceitar.nome} />
                  <AvatarFallback>
                    {pacienteParaAceitar.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h4 className="font-semibold">{pacienteParaAceitar.nome}</h4>
                  <p className="text-sm text-gray-600">
                    {pacienteParaAceitar.idade} anos • {pacienteParaAceitar.genero}
                  </p>
                  <Badge
                    status={getUrgencyBadgeStatus(pacienteParaAceitar.nivelUrgencia)}
                    className="flex items-center gap-1 w-fit mt-1"
                  >
                    {getUrgencyIcon(pacienteParaAceitar.nivelUrgencia)}
                    {pacienteParaAceitar.nivelUrgencia}
                  </Badge>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h5 className="font-medium text-blue-900 mb-2">Instruções importantes:</h5>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Você será redirecionado para o chat com o paciente</li>
                  <li>• O atendimento deve ser iniciado imediatamente</li>
                  <li>• Mantenha um atendimento profissional e empático</li>
                  <li>• Documente adequadamente o atendimento</li>
                </ul>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Tempo na fila:</span>
                  <p className="text-gray-600">{formatarTempoFila(pacienteParaAceitar.tempoNaFilaMinutes)}</p>
                </div>
                <div>
                  <span className="font-medium">Valor:</span>
                  <p className="text-gray-600">R$ {pacienteParaAceitar.amount.toFixed(2)}</p>
                </div>
              </div>

              <div>
                <span className="font-medium">Motivo da consulta:</span>
                <p className="text-gray-700 bg-gray-50 p-2 rounded mt-1">
                  {pacienteParaAceitar.sintomasResumo}
                </p>
              </div>
            </div>
          )}

          <AlertDialogFooter>
            <AlertDialogCancel disabled={acceptAppointmentMutation.isPending}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmarAceiteAtendimento}
              disabled={acceptAppointmentMutation.isPending}
              className="bg-primary"
            >
              {acceptAppointmentMutation.isPending ? (
                <span className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Aceitando...
                </span>
              ) : (
                "Sim, aceitar atendimento"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
