"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import {
  Clock,
  AlertCircle,
  AlertTriangle,
  Info,
  UserRound,
  Users,
  CheckCircle,
  MessageCircle,
  Eye,
  Calendar,
  DollarSign
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@ui/components/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { apiClient } from "@shared/lib/api-client";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Interface para os pacientes na fila
interface PacienteFila {
  id: string;
  nome: string;
  fotoUrl: string;
  idade: number;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  sintomasResumo: string;
  nivelUrgencia: 'Nível 1 (Emergência)' | 'Nível 2 (Urgência)' | 'Nível 3 (Pouco Urgente)';
  tempoNaFilaMinutes: number;
  motivoCompleto?: string;
  localizacaoAproximada?: string;
  planoSaudeStatus?: 'Verificado' | 'Pagamento Pendente' | 'N/A';
  appointmentId: string;
  queuePosition: number;
  waitTimeMinutes: number;
  urgencyLevel: 'HIGH' | 'MEDIUM' | 'LOW';
  amount: number;
  paymentStatus: string;
  status: string;
  acceptedAt?: string | null;
  scheduledAt: string; // Data e horário da consulta
}

export function EnhancedPlantaoClient() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("available");
  const [pacienteSelecionado, setPacienteSelecionado] = useState<PacienteFila | null>(null);
  const [dialogAberto, setDialogAberto] = useState(false);
  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false);
  const [pacienteParaAceitar, setPacienteParaAceitar] = useState<PacienteFila | null>(null);

  // Format date and time - moved to main component scope
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    const isOverdue = date < now;

    const timeString = date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });

    let formattedDateString = '';
    if (isToday) {
      formattedDateString = `Hoje às ${timeString}`;
    } else {
      formattedDateString = date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }) + ` às ${timeString}`;
    }

    return { dateString: formattedDateString, isOverdue };
  };

  const { data: availableQueueData, isLoading: isLoadingAvailable, refetch: refetchAvailable } =
    apiClient.onDuty.getQueue.useQuery({
      status: "SCHEDULED",
      showOnlyUnassigned: true
    }, {
      refetchInterval: 30000,
    });

  const { data: doctorQueueData, isLoading: isLoadingDoctor, refetch: refetchDoctor } =
    apiClient.onDuty.getDoctorQueue.useQuery({
      status: undefined
    }, {
      refetchInterval: 15000,
    });

  useEffect(() => {
    // Initial data fetch
    refetchAvailable();
    refetchDoctor();

    // Set up interval for refreshing data
    const intervalId = setInterval(() => {
      refetchAvailable();
      refetchDoctor();
    }, 15000); // Refresh every 15 seconds

    return () => clearInterval(intervalId);
  }, [refetchAvailable, refetchDoctor]);

  const acceptAppointmentMutation = apiClient.onDuty.acceptAppointment.useMutation({
    onSuccess: (data) => {
      toast.success("Atendimento aceito com sucesso!");
      refetchAvailable();
      refetchDoctor();
      setDialogAberto(false);
      setPacienteSelecionado(null);
      setConfirmationModalOpen(false);
      setPacienteParaAceitar(null);

      if (data?.id) {
        			router.push(`/app/zapchat?appointment=${data.id}`);
      } else {
        			router.push('/app/zapchat');
      }
    },
    onError: (error) => {
      console.error("Erro ao aceitar atendimento:", error);
      toast.error("Erro ao aceitar atendimento: " + error.message);
      setConfirmationModalOpen(false);
      setPacienteParaAceitar(null);
    }
  });

  const convertApiDataToPatients = (data: any, isDoctorQueue = false): PacienteFila[] => {
    if (!data) return [];
    
    // getDoctorQueue retorna array direto, getQueue retorna { items, hasNextPage, nextCursor }
    const appointments = isDoctorQueue ? data : (data.items || []);

    return appointments.map((appointment: any, index: number) => {
      const urgencyMap: Record<string, 'Nível 1 (Emergência)' | 'Nível 2 (Urgência)' | 'Nível 3 (Pouco Urgente)'> = {
        'HIGH': 'Nível 1 (Emergência)',
        'MEDIUM': 'Nível 2 (Urgência)',
        'LOW': 'Nível 3 (Pouco Urgente)'
      };

      const patient = appointment.patient;
      const user = patient?.user;

      const idade = patient?.birthDate
        ? Math.floor((Date.now() - new Date(patient.birthDate).getTime()) / (365.25 * 24 * 60 * 60 * 1000))
        : Math.floor(Math.random() * 50) + 20;

      return {
        id: appointment.id,
        appointmentId: appointment.id,
        nome: user?.name || 'Paciente',
        fotoUrl: user?.avatarUrl ||  null,
        idade,
        genero: patient?.gender || (Math.random() > 0.5 ? 'Masculino' : 'Feminino'),
        sintomasResumo: appointment.symptoms || 'Consulta de plantão',
        nivelUrgencia: urgencyMap[appointment.urgencyLevel as keyof typeof urgencyMap] || 'Nível 3 (Pouco Urgente)',
        tempoNaFilaMinutes: appointment.waitTimeMinutes || 0,
        motivoCompleto: appointment.symptoms || 'Paciente solicitou atendimento de plantão',
        localizacaoAproximada: 'Brasil',
        planoSaudeStatus: appointment.paymentStatus === 'PAID' ? 'Verificado' : 'Pagamento Pendente',
        queuePosition: appointment.queuePosition || index + 1,
        waitTimeMinutes: appointment.waitTimeMinutes || 0,
        urgencyLevel: appointment.urgencyLevel || 'LOW',
        amount: Number(appointment.amount) || 0,
        paymentStatus: appointment.paymentStatus || 'PENDING',
        status: appointment.status || 'SCHEDULED',
        acceptedAt: appointment.acceptedAt,
        scheduledAt: appointment.scheduledAt
      };
    });
  };

  const availablePatients = convertApiDataToPatients(availableQueueData, false);
  const doctorPatients = convertApiDataToPatients(doctorQueueData, true);

  // Ordenar pacientes por urgência (padrão)
  const sortPatients = (patients: PacienteFila[]) => {
    return [...patients].sort((a, b) => {
      const urgencyOrder = {
        'Nível 1 (Emergência)': 3,
        'Nível 2 (Urgência)': 2,
        'Nível 3 (Pouco Urgente)': 1
      };
      const urgencyDiff = urgencyOrder[a.nivelUrgencia] - urgencyOrder[b.nivelUrgencia];
      if (urgencyDiff !== 0) return -urgencyDiff;
      return b.tempoNaFilaMinutes - a.tempoNaFilaMinutes;
    });
  };

  const sortedAvailablePatients = sortPatients(availablePatients);
  const sortedDoctorPatients = sortPatients(doctorPatients);

  // Funções auxiliares
  const abrirConfirmacaoAceite = (paciente: PacienteFila) => {
    if (paciente.paymentStatus !== 'PAID') {
      toast.error("Não é possível aceitar atendimento com pagamento pendente");
      return;
    }
    setPacienteParaAceitar(paciente);
    setConfirmationModalOpen(true);
  };

  const confirmarAceiteAtendimento = () => {
    if (!pacienteParaAceitar) return;
    acceptAppointmentMutation.mutate({
      appointmentId: pacienteParaAceitar.appointmentId
    });
  };

  const abrirDetalhesPaciente = (paciente: PacienteFila) => {
    setPacienteSelecionado(paciente);
    setDialogAberto(true);
  };

  const abrirChat = (paciente: PacienteFila) => {
    router.push(`/app/zapchat?appointment=${paciente.appointmentId}`);
  };

  const formatarTempoFila = (minutos: number) => {
    if (minutos < 60) {
      return `${minutos} MIN`;
    } else {
      const horas = Math.floor(minutos / 60);
      const minutosRestantes = minutos % 60;
      return `${horas}H ${minutosRestantes}MIN`;
    }
  };

  // Função para obter o status do badge de urgência
  const getUrgencyBadgeStatus = (nivelUrgencia: string) => {
    if (nivelUrgencia.includes("Emergência")) {
      return "bg-red-100 text-red-800 border-red-200";
    } else if (nivelUrgencia.includes("Urgência")) {
      return "bg-amber-100 text-amber-800 border-amber-200";
    } else {
      return "bg-green-100 text-green-800 border-green-200";
    }
  };

  // Função para obter o ícone de urgência
  const getUrgencyIcon = (nivelUrgencia: string) => {
    if (nivelUrgencia.includes("Emergência")) {
      return <AlertTriangle className="h-3 w-3" />;
    } else if (nivelUrgencia.includes("Urgência")) {
      return <AlertCircle className="h-3 w-3" />;
    } else {
      return <Info className="h-3 w-3" />;
    }
  };

  if (isLoadingAvailable && isLoadingDoctor) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando filas de plantão...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Tabs defaultValue="available" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4 w-full grid grid-cols-2 tabs-mobile">
          <TabsTrigger value="available" className="flex items-center justify-center gap-2 py-3">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Pacientes na Fila</span>
            <span className="sm:hidden">Na Fila</span>
            {sortedAvailablePatients.length > 0 && (
              <Badge className="ml-1 bg-blue-100 text-blue-800 text-xs">
                {sortedAvailablePatients.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="accepted" className="flex items-center justify-center gap-2 py-3">
            <UserRound className="h-4 w-4" />
            <span className="hidden sm:inline">Meus Pacientes</span>
            <span className="sm:hidden">Meus</span>
            {sortedDoctorPatients.length > 0 && (
              <Badge className="ml-1 bg-green-100 text-green-800 text-xs">
                {sortedDoctorPatients.length}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="available" className="space-y-4">
          {isLoadingAvailable ? (
            <div className="flex justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : sortedAvailablePatients.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10">
                <Info className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-700">Nenhum paciente na fila</h3>
                <p className="text-gray-500 text-center mt-2">
                  No momento não há pacientes aguardando atendimento de plantão.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div>
              {sortedAvailablePatients.map((paciente) => (
                <PatientCard
                  key={paciente.id}
                  paciente={paciente}
                  isAccepted={false}
                  onAccept={() => abrirConfirmacaoAceite(paciente)}
                  onViewDetails={() => abrirDetalhesPaciente(paciente)}
                  onOpenChat={() => abrirChat(paciente)}
                  isAccepting={acceptAppointmentMutation.isPending}
                  formatarTempoFila={formatarTempoFila}
                  getUrgencyIcon={getUrgencyIcon}
                  getUrgencyBadgeStatus={getUrgencyBadgeStatus}
                  formatDateTime={formatDateTime}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="accepted" className="space-y-4">
          {isLoadingDoctor ? (
            <div className="flex justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : sortedDoctorPatients.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10">
                <UserRound className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-700">Nenhum paciente aceito</h3>
                <p className="text-gray-500 text-center mt-2">
                  Você ainda não aceitou nenhum paciente para atendimento.
                  <br />
                  Aceite pacientes da fila para começar a atender.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div>
              {sortedDoctorPatients.map((paciente) => (
                <PatientCard
                  key={paciente.id}
                  paciente={paciente}
                  isAccepted={true}
                  onAccept={() => {}}
                  onViewDetails={() => abrirDetalhesPaciente(paciente)}
                  onOpenChat={() => abrirChat(paciente)}
                  isAccepting={false}
                  formatarTempoFila={formatarTempoFila}
                  getUrgencyIcon={getUrgencyIcon}
                  getUrgencyBadgeStatus={getUrgencyBadgeStatus}
                  formatDateTime={formatDateTime}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Patient Details Dialog */}
      <Dialog open={dialogAberto} onOpenChange={setDialogAberto}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Detalhes do Paciente</DialogTitle>
            <DialogDescription>
              Informações detalhadas sobre o paciente e sua condição
            </DialogDescription>
          </DialogHeader>

          {pacienteSelecionado && (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16 border">
                  {pacienteSelecionado.fotoUrl ? (
                    <AvatarImage src={pacienteSelecionado.fotoUrl} alt={pacienteSelecionado.nome} />
                  ) : (
                    <AvatarFallback>
                      <UserRound className="h-8 w-8" />
                    </AvatarFallback>
                  )}
                </Avatar>
                <div>
                  <h3 className="text-lg font-semibold">{pacienteSelecionado.nome}</h3>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <span>{pacienteSelecionado.idade} anos</span>
                    <span>•</span>
                    <span>{pacienteSelecionado.genero}</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Nível de Urgência</h4>
                  <Badge className={cn("mt-1", getUrgencyBadgeStatus(pacienteSelecionado.nivelUrgencia))}>
                    {getUrgencyIcon(pacienteSelecionado.nivelUrgencia)}
                    <span className="ml-1">{pacienteSelecionado.nivelUrgencia}</span>
                  </Badge>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Tempo na Fila</h4>
                  <p className="font-medium">{formatarTempoFila(pacienteSelecionado.tempoNaFilaMinutes)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status de Pagamento</h4>
                  <Badge className={cn("mt-1",
                    pacienteSelecionado.paymentStatus === 'PAID' ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                  )}>
                    {pacienteSelecionado.paymentStatus === 'PAID' ? 'Pago' : 'Pendente'}
                  </Badge>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Valor</h4>
                  <p className="font-medium">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(pacienteSelecionado.amount)}
                  </p>
                </div>
              </div>

              {/* Data e horário da consulta */}
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                <h4 className="text-sm font-medium text-blue-800 mb-2">📅 Informações da Consulta</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-xs font-medium text-blue-600">Data e Horário</h5>
                    <p className="text-sm font-medium text-blue-800">
                      {formatDateTime(pacienteSelecionado.scheduledAt).dateString}
                      {formatDateTime(pacienteSelecionado.scheduledAt).isOverdue && (
                        <span className="ml-2 text-red-500"> (Atrasado)</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium text-blue-600">Posição na Fila</h5>
                    <p className="text-sm font-medium text-blue-800">
                      #{pacienteSelecionado.queuePosition}
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-500">Sintomas Relatados</h4>
                <p className="mt-1 text-gray-700 bg-gray-50 p-3 rounded border">
                  {pacienteSelecionado.sintomasResumo || 'Nenhum sintoma relatado'}
                </p>
              </div>
            </div>
          )}

          <DialogFooter>
            {pacienteSelecionado && (
              <>
                <Button variant="outline" onClick={() => setDialogAberto(false)}>
                  Fechar
                </Button>

                {pacienteSelecionado.acceptedAt ? (
                  <Button onClick={() => abrirChat(pacienteSelecionado)}>
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Ir para Chat
                  </Button>
                ) : (
                  <Button
                    onClick={() => {
                      setDialogAberto(false);
                      abrirConfirmacaoAceite(pacienteSelecionado);
                    }}
                    disabled={acceptAppointmentMutation.isPending || pacienteSelecionado.paymentStatus !== 'PAID'}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Aceitar Paciente
                  </Button>
                )}
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmationModalOpen} onOpenChange={setConfirmationModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Aceitação</AlertDialogTitle>
            <AlertDialogDescription>
              Você está prestes a aceitar este paciente para atendimento imediato.
              Após aceitar, você será responsável pelo atendimento e o paciente será notificado.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={confirmarAceiteAtendimento}>
              Confirmar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

// Componente para card de paciente
interface PatientCardProps {
  paciente: PacienteFila;
  isAccepted: boolean;
  onAccept: () => void;
  onViewDetails: () => void;
  onOpenChat: () => void;
  isAccepting: boolean;
  formatarTempoFila: (minutos: number) => string;
  getUrgencyIcon: (nivel: string) => React.ReactNode;
  getUrgencyBadgeStatus: (nivel: string) => string;
  formatDateTime: (dateString: string) => { dateString: string; isOverdue: boolean };
}

function PatientCard({ paciente, isAccepted, onAccept, onViewDetails, onOpenChat, isAccepting, formatarTempoFila, getUrgencyIcon, getUrgencyBadgeStatus, formatDateTime }: PatientCardProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <Card className={cn("mb-4 overflow-hidden relative patient-card-mobile",
      formatDateTime(paciente.scheduledAt).isOverdue && "border-red-200 bg-red-50/30"
    )}>


      <CardContent className="p-0">
        <div className="p-4">
          {/* Header com informações principais */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <Avatar className="h-12 w-12 border flex-shrink-0">
                {paciente.fotoUrl ? (
                  <AvatarImage src={paciente.fotoUrl} alt={paciente.nome} />
                ) : (
                  <AvatarFallback>
                    <UserRound className="h-6 w-6" />
                  </AvatarFallback>
                )}
              </Avatar>
              <div className="min-w-0 flex-1">
                <h3 className="font-medium text-gray-900 truncate">{paciente.nome}</h3>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <span>{paciente.idade} anos</span>
                  <span>•</span>
                  <span>{paciente.genero}</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col items-end gap-2 ml-3">
              {/* Badge de urgência otimizado - sem redundância */}


              <div className="flex flex-row gap-2">

              <Badge className={cn("urgency-badge", getUrgencyBadgeStatus(paciente.nivelUrgencia))}>
                {getUrgencyIcon(paciente.nivelUrgencia)}
                <span className="text-xs font-semibold">
                  {paciente.nivelUrgencia.includes("Emergência") ? "EMERGÊNCIA" :
                   paciente.nivelUrgencia.includes("Urgência") ? "URGÊNCIA" : "NORMAL"}
                </span>
              </Badge>
                 {/* Badge de prioridade para consultas atrasadas - removido redundância */}
      {formatDateTime(paciente.scheduledAt).isOverdue && (
        <div className="hidden md:flex bg-red-500 text-white items-center rounded-sm text-xs px-2 py-1 font-medium">
          ⚠️ ATRASADO
        </div>
      )}
              </div>


              <div className="flex items-center text-xs text-gray-500">
                <Clock className="h-3 w-3 mr-1" />
                <span>{formatarTempoFila(paciente.tempoNaFilaMinutes)}</span>
              </div>
            </div>
          </div>

          {/* Data e horário da consulta */}
          <div className={cn("mb-3 p-3 rounded-lg border",
            formatDateTime(paciente.scheduledAt).isOverdue
              ? "bg-red-50 border-red-200"
              : "bg-blue-50 border-blue-100"
          )}>
            <div className="flex items-center gap-2 text-sm">
              <Calendar className={cn("h-4 w-4",
                formatDateTime(paciente.scheduledAt).isOverdue ? "text-red-600" : "text-blue-600"
              )} />
              <span className={cn("font-medium",
                formatDateTime(paciente.scheduledAt).isOverdue ? "text-red-800" : "text-blue-800"
              )}>
                Consulta marcada:
              </span>
              <span className={cn(
                formatDateTime(paciente.scheduledAt).isOverdue ? "text-red-700" : "text-blue-700"
              )}>
                {formatDateTime(paciente.scheduledAt).dateString}
              </span>
            </div>
          </div>

          {/* Sintomas */}
          <div className="mb-3">
            <h4 className="text-sm font-medium text-gray-700 mb-1">Sintomas:</h4>
            <p className="text-sm text-gray-600 line-clamp-2 bg-gray-50 p-2 rounded border">
              {paciente.sintomasResumo}
            </p>
          </div>

          {/* Informações da fila e ações */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex items-center gap-2 flex-wrap">
              <Badge className="bg-blue-50 text-blue-700 border-blue-200 text-xs flex items-center">
                <Users className="h-3 w-3 mr-1" />
                Posição: {paciente.queuePosition}
              </Badge>
              <Badge className="bg-green-50 text-green-700 border-green-200 text-xs flex items-center">
                <DollarSign className="h-3 w-3 mr-1" />
                {formatCurrency(paciente.amount)}
              </Badge>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={onViewDetails} className="flex-1 sm:flex-none mobile-button">
                <Eye className="h-4 w-4 mr-1" />
                Detalhes
              </Button>

              {isAccepted ? (
                <Button size="sm" variant="default" onClick={onOpenChat} className="flex-1 sm:flex-none mobile-button">
                  <MessageCircle className="h-4 w-4 mr-1" />
                  Atender
                </Button>
              ) : (
                <Button
                  size="sm"
                  variant="default"
                  onClick={onAccept}
                  disabled={isAccepting || paciente.paymentStatus !== 'PAID'}
                  className="bg-green-600 hover:bg-green-700 flex-1 sm:flex-none mobile-button"
                >
                  {isAccepting ? (
                    <>Aceitando...</>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Aceitar
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
