// /apps/web/app/[locale]/(app)/app/plantao/components/plantao-skeleton.tsx
import { Card, CardContent } from "@ui/components/card";
import { cn } from "@ui/lib";
import { Clock, AlertCircle, AlertTriangle, Info, Search, UserRound, Calendar, Users, Activity } from "lucide-react";

export function PlantaoSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header melhorado com informações úteis para médicos */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
          {/* Lado esquerdo - Título e data */}
          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-gray-900">Plantão Médico</h1>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>sexta-feira, 3 de outubro de 2025</span>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>08:00 - 20:00</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>21 médicos online</span>
              </div>
            </div>
          </div>

          {/* Lado direito - Status e busca */}
          <div className="flex flex-col sm:flex-row items-center gap-4 w-full lg:w-auto">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-600">ONLINE</span>
              </div>
              <div className="text-sm text-gray-500">
                Atualizado em tempo real
              </div>
            </div>

            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                placeholder="Buscar paciente ou sintoma..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled
              />
            </div>
          </div>
        </div>
      </div>

      {/* Estatísticas da fila - apenas números ficam loading */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Card 1: Pacientes na Fila */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Pacientes na Fila</p>
                <div className="h-8 w-12 bg-gray-200 rounded animate-pulse"></div>
                <p className="text-xs text-gray-500">0 na última hora</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '0%' }}></div>
                </div>
                <p className="text-xs text-gray-500">Capacidade: 0%</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Card 2: Casos Urgentes */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Casos Urgentes</p>
                <div className="h-8 w-12 bg-gray-200 rounded animate-pulse"></div>
                <p className="text-xs text-gray-500">Nenhum caso crítico</p>
              </div>
              <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Card 3: Tempo Médio */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Tempo Médio</p>
                <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
                <p className="text-xs text-gray-500">Tempo médio na fila</p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Card 4: Seus Atendimentos */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Seus Atendimentos</p>
                <div className="h-8 w-12 bg-gray-200 rounded animate-pulse"></div>
                <p className="text-xs text-gray-500">Em andamento</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                <Activity className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs e Lista */}
      <div className="bg-white rounded-lg border">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button className="py-4 px-1 border-b-2 border-blue-500 text-blue-600 font-medium text-sm">
              Pacientes na Fila
            </button>
            <button className="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
              Meus Pacientes
            </button>
          </nav>
        </div>

        {/* Conteúdo da lista */}
        <div className="p-6">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Info className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum paciente na fila</h3>
              <p className="text-gray-500">No momento não há pacientes aguardando atendimento de plantão.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Versão com shimmer effect (opcional, mais sofisticado)
export function PlantaoSkeletonWithShimmer() {
  const shimmerClass = "relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/60 before:to-transparent";
  
  return (
    <div className="space-y-6">
      {/* Header com status e busca */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className={cn("w-3 h-3 bg-gray-200 rounded-full", shimmerClass)}></div>
            <div className={cn("h-4 w-16 bg-gray-200 rounded", shimmerClass)}></div>
          </div>
          <div className={cn("h-4 w-40 bg-gray-200 rounded", shimmerClass)}></div>
        </div>

        <div className={cn("w-full sm:w-80 h-10 bg-gray-200 rounded-md", shimmerClass)}></div>
      </div>

      {/* Estatísticas da fila */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <div className={cn("h-5 w-5 bg-gray-200 rounded", shimmerClass)}></div>
                <div className="space-y-2 flex-1">
                  <div className={cn("h-4 w-20 bg-gray-200 rounded", shimmerClass)}></div>
                  <div className={cn("h-8 w-12 bg-gray-200 rounded", shimmerClass)}></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Título da lista */}
      <div className="flex items-center gap-2">
        <div className={cn("h-5 w-5 bg-gray-200 rounded", shimmerClass)}></div>
        <div className={cn("h-6 w-48 bg-gray-200 rounded", shimmerClass)}></div>
      </div>

      {/* Lista de pacientes - 3 cards skeleton */}
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 flex-1">
                  {/* Avatar */}
                  <div className={cn("h-12 w-12 bg-gray-200 rounded-full shrink-0", shimmerClass)}></div>

                  <div className="flex-1 space-y-3">
                    {/* Nome e Badge */}
                    <div className="flex items-center gap-2">
                      <div className={cn("h-6 w-40 bg-gray-200 rounded", shimmerClass)}></div>
                      <div className={cn("h-6 w-32 bg-gray-200 rounded-full", shimmerClass)}></div>
                    </div>

                    {/* Info do paciente (idade, gênero, localização) */}
                    <div className={cn("h-4 w-64 bg-gray-200 rounded", shimmerClass)}></div>

                    {/* Sintomas (2 linhas) */}
                    <div className="space-y-2">
                      <div className={cn("h-4 w-full bg-gray-200 rounded", shimmerClass)}></div>
                      <div className={cn("h-4 w-3/4 bg-gray-200 rounded", shimmerClass)}></div>
                    </div>

                    {/* Tempo, Status e Valor */}
                    <div className="flex items-center gap-4">
                      <div className={cn("h-4 w-20 bg-gray-200 rounded", shimmerClass)}></div>
                      <div className={cn("h-6 w-28 bg-gray-200 rounded-full", shimmerClass)}></div>
                      <div className={cn("h-4 w-24 bg-gray-200 rounded", shimmerClass)}></div>
                    </div>
                  </div>
                </div>

                {/* Botões */}
                <div className="flex gap-2 shrink-0">
                  <div className={cn("h-9 w-28 bg-gray-200 rounded-md", shimmerClass)}></div>
                  <div className={cn("h-9 w-40 bg-gray-200 rounded-md", shimmerClass)}></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

