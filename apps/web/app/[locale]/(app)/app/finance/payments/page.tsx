import { currentUser } from "@saas/auth/lib/current-user";
import { UserRoleSchema } from "database";
import { redirect } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Receipt, DollarSign, TrendingUp, TrendingDown, CreditCard, Users } from "lucide-react";
import { PaginationButton } from "@ui/components/pagination-button";

export default async function PaymentsPage({
  searchParams,
}: { searchParams?: { page?: string; search?: string; status?: string; type?: string } }) {
  const session = await currentUser();

  if (!session?.user || (session.user.role !== UserRoleSchema.Values.ADMIN && session.user.role !== UserRoleSchema.Values.DOCTOR)) {
    redirect("/app/dashboard");
  }

  const page = Number(searchParams?.page || 1);
  const search = searchParams?.search || "";
  const status = searchParams?.status || "";
  const type = searchParams?.type || "";

  // Buscar dados reais da API
  const apiUrl = new URL('/api/finance/payments', process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000');
  apiUrl.searchParams.set('page', page.toString());
  if (search) apiUrl.searchParams.set('search', search);
  if (status) apiUrl.searchParams.set('status', status);
  if (type) apiUrl.searchParams.set('type', type);

  let paymentsData;
  try {
    const response = await fetch(apiUrl.toString(), {
      headers: {
        'Cookie': `auth-token=${session.user.id}`, // Simular autenticação
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch payments data');
    }
    
    paymentsData = await response.json();
  } catch (error) {
    console.error('Error fetching payments:', error);
    // Fallback para dados vazios em caso de erro
    paymentsData = {
      transactions: [],
      pagination: { page: 1, totalPages: 1, totalCount: 0 },
      metrics: { totalTransactions: 0, paidTransactions: 0, pendingTransactions: 0, failedTransactions: 0, totalRevenue: 0, successRate: 0 }
    };
  }

  const { transactions, pagination, metrics } = paymentsData;
  const { totalTransactions, paidTransactions, pendingTransactions, failedTransactions, totalRevenue, successRate } = metrics;

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(amount);

  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("pt-BR", {
      dateStyle: "short",
      timeStyle: "short"
    }).format(date);
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      PAID: { label: "Pago", className: "bg-green-100 text-green-800" },
      PENDING: { label: "Pendente", className: "bg-yellow-100 text-yellow-800" },
      FAILED: { label: "Falhou", className: "bg-red-100 text-red-800" },
      REFUNDED: { label: "Reembolsado", className: "bg-gray-100 text-gray-800" }
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, className: "bg-gray-100 text-gray-800" };

    return (
      <Badge className={statusInfo.className}>
        {statusInfo.label}
      </Badge>
    );
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "SUBSCRIPTION":
        return <CreditCard className="h-4 w-4 text-blue-600" />;
      case "CONSULTATION":
        return <Receipt className="h-4 w-4 text-green-600" />;
      default:
        return <Receipt className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "SUBSCRIPTION":
        return "Assinatura";
      case "CONSULTATION":
        return "Consulta";
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold flex items-center gap-2">
          <Receipt className="h-6 w-6" />
          Pagamentos
        </h1>
        <p className="text-sm text-muted-foreground mt-2">
          Histórico de pagamentos e transações financeiras
        </p>
      </div>

      {/* Métricas com novo padrão */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Total de Pagamentos
                </p>
                <h3 className="text-3xl font-bold text-blue-600">
                  {totalTransactions}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Todas as transações
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                <Receipt className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Receita Total
                </p>
                <h3 className="text-3xl font-bold text-green-600">
                  {formatCurrency(totalRevenue)}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Valor total arrecadado
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Taxa de Sucesso
                </p>
                <h3 className="text-3xl font-bold text-purple-600">
                  {successRate}%
                </h3>
                <div className="flex items-center mt-2 text-xs text-muted-foreground">
                  <div className={`flex items-center mr-2 ${successRate >= 80 ? 'text-green-600' : successRate >= 60 ? 'text-muted-foreground' : 'text-red-600'}`}>
                    {successRate >= 80 ? <TrendingUp className="h-3 w-3" /> : successRate >= 60 ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
                    <span className="ml-1">{successRate}%</span>
                  </div>
                  <span>Pagamentos aprovados</span>
                </div>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Pendentes
                </p>
                <h3 className="text-3xl font-bold text-orange-600">
                  {pendingTransactions}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Aguardando pagamento
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Pagamentos */}
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Pagamentos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-muted rounded-full">
                    {getTypeIcon(transaction.type)}
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{transaction.description}</span>
                      {getStatusBadge(transaction.status)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {transaction.patientName} • {transaction.doctorName}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {getTypeLabel(transaction.type)} • {transaction.paymentMethod}
                    </div>
                  </div>
                </div>
                <div className="text-right space-y-1">
                  <div className="font-semibold text-lg">
                    {formatCurrency(transaction.amount)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {transaction.paidAt ? formatDateTime(transaction.paidAt) : formatDateTime(transaction.createdAt)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Paginação */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center">
          <PaginationButton
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            baseUrl="/app/finance/payments"
            searchParams={{
              ...(search ? { search } : {}),
              ...(status ? { status } : {}),
              ...(type ? { type } : {})
            }}
          />
        </div>
      )}
    </div>
  );
}
