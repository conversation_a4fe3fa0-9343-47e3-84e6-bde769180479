"use client";

import { useState, useCallback, useEffect } from "react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@ui/components/command";
import { Badge } from "@ui/components/badge";
import { X, Check } from "lucide-react";
import { cn } from "@ui/lib";

// Lista de CIDs mais comuns (pode ser expandida ou buscada de uma API)
const COMMON_CIDS = [
  { code: "A09", description: "Diarreia e gastroenterite de origem infecciosa presumível" },
  { code: "B34.2", description: "Infecção por coronavírus de localização não especificada" },
  { code: "I10", description: "Hipertensão essencial (primária)" },
  { code: "E11", description: "Diabetes mellitus não-insulino-dependente" },
  { code: "J00", description: "Nasofaringite aguda (resfriado comum)" },
  { code: "J06.9", description: "Infecção aguda das vias aéreas superiores não especificada" },
  { code: "J18.9", description: "Pneumonia não especificada" },
  { code: "J20.9", description: "Bronquite aguda não especificada" },
  { code: "K29.7", description: "Gastrite não especificada" },
  { code: "R10.4", description: "Outras dores abdominais e as não especificadas" },
  { code: "R51", description: "Cefaleia" },
  { code: "R05", description: "Tosse" },
  { code: "R06.0", description: "Dispneia" },
  { code: "R50.9", description: "Febre não especificada" },
  { code: "M54.5", description: "Dor lombar baixa" },
  { code: "M79.1", description: "Mialgia" },
  { code: "N39.0", description: "Infecção do trato urinário de localização não especificada" },
  { code: "L03.9", description: "Celulite não especificada" },
  { code: "Z00.0", description: "Exame médico geral" },
  { code: "Z01.4", description: "Exame ginecológico" }
];

interface CidItem {
  code: string;
  description: string;
}

interface CidInputProps {
  value: CidItem[];
  onChange: (cids: CidItem[]) => void;
  disabled?: boolean;
  required?: boolean;
}

export function CidInput({ value = [], onChange, disabled = false, required = false }: CidInputProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");

  // Filtrar CIDs baseado na busca
  const filteredCids = COMMON_CIDS.filter((cid) => {
    const searchLower = search.toLowerCase();
    return (
      cid.code.toLowerCase().includes(searchLower) ||
      cid.description.toLowerCase().includes(searchLower)
    );
  });

  // Adicionar CID
  const addCid = useCallback((cid: CidItem) => {
    // Verificar se já não está na lista
    if (!value.some(c => c.code === cid.code)) {
      onChange([...value, cid]);
    }
    setSearch("");
    setOpen(false);
  }, [value, onChange]);

  // Remover CID
  const removeCid = useCallback((code: string) => {
    onChange(value.filter(c => c.code !== code));
  }, [value, onChange]);

  // Permitir adicionar CID customizado digitado manualmente
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && search.length >= 2) {
      e.preventDefault();
      
      // Verificar se o código já existe nos CIDs comuns
      const existingCid = COMMON_CIDS.find(
        c => c.code.toLowerCase() === search.toUpperCase()
      );
      
      if (existingCid) {
        addCid(existingCid);
      } else {
        // Adicionar CID customizado
        const customCid = {
          code: search.toUpperCase(),
          description: "CID customizado"
        };
        addCid(customCid);
      }
    }
  }, [search, addCid]);

  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <label className="text-sm font-medium flex items-center gap-1">
          CID - Código Internacional de Doenças
          {required && <span className="text-red-500">*</span>}
        </label>
        
        {/* Lista de CIDs selecionados */}
        {value.length > 0 && (
          <div className="flex flex-wrap gap-2 p-3 border rounded-lg bg-muted/30">
            {value.map((cid) => (
              <Badge
                key={cid.code}
                variant="secondary"
                className="px-3 py-1.5 text-sm flex items-center gap-2"
              >
                <span className="font-mono font-semibold">{cid.code}</span>
                <span className="text-xs text-muted-foreground max-w-[200px] truncate">
                  {cid.description}
                </span>
                {!disabled && (
                  <button
                    type="button"
                    onClick={() => removeCid(cid.code)}
                    className="ml-1 hover:text-destructive transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        )}

        {/* Input de busca */}
        {!disabled && (
          <Command className="rounded-lg border">
            <CommandInput 
              placeholder="Buscar CID por código ou descrição (ex: A09, diabetes, hipertensão)..."
              value={search}
              onValueChange={setSearch}
              onKeyDown={handleKeyDown}
              className="h-10"
            />
            <CommandList className="max-h-[200px]">
              <CommandEmpty>
                {search.length >= 2 ? (
                  <div className="p-4 text-sm text-center">
                    <p className="text-muted-foreground mb-2">
                      Nenhum CID encontrado para "{search}"
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Pressione Enter para adicionar "{search.toUpperCase()}" como CID customizado
                    </p>
                  </div>
                ) : (
                  "Digite pelo menos 2 caracteres para buscar..."
                )}
              </CommandEmpty>
              <CommandGroup>
                {filteredCids.map((cid) => {
                  const isSelected = value.some(c => c.code === cid.code);
                  return (
                  <CommandItem
                    key={cid.code}
                    value={`${cid.code} ${cid.description}`}
                    onSelect={() => addCid(cid)}
                    className={cn(
                      "flex items-center gap-2 cursor-pointer",
                      isSelected && "opacity-50"
                    )}
                  >
                    <Check
                      className={cn(
                        "h-4 w-4",
                        isSelected ? "opacity-100" : "opacity-0"
                      )}
                    />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-mono font-semibold text-sm">{cid.code}</span>
                          <span className="text-xs text-muted-foreground">
                            {cid.description}
                          </span>
                        </div>
                      </div>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        )}
      </div>

      {required && value.length === 0 && (
        <p className="text-xs text-red-500">
          É obrigatório informar pelo menos um CID para finalizar a consulta
        </p>
      )}

      <p className="text-xs text-muted-foreground">
        Selecione um ou mais CIDs que correspondam ao diagnóstico do paciente. 
        Você pode buscar por código (ex: A09) ou descrição (ex: diabetes).
      </p>
    </div>
  );
}

