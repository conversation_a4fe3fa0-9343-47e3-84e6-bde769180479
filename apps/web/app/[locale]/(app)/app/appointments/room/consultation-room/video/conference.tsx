import { useEffect, useState, useCallback, useRef } from "react";

import { LiveKitRoom, VideoConference } from "@livekit/components-react";
import { Loader2, Video, RefreshCcw, AlertCircle } from "lucide-react";

import "@livekit/components-styles";

import { toast } from "sonner";

import { useAppointmentStatus } from "../hooks/use-appointment-status";
import { getLiveKitToken } from "../../../../../../../../actions/appointments/livekit/livekit";
import { Card } from "@ui/components/card";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";

interface VideoRoomProps {
	appointmentId: string;
	userRole: "DOCTOR" | "PATIENT";
	isActive: boolean;
	isCompleted?: boolean;
	onError?: (error: Error) => void;
	onStatusChange?: (status: { isActive: boolean, isCompleted: boolean }) => void;
}

export function VideoRoom({
	appointmentId,
	userRole,
	isActive: initialIsActive,
	isCompleted: initialIsCompleted = false,
	onError,
	onStatusChange,
}: VideoRoomProps) {
	const [token, setToken] = useState<string | null>(null);
	const [isConnecting, setIsConnecting] = useState(false);
	const [connectionError, setConnectionError] = useState<Error | null>(null);
	const [reconnectAttempts, setReconnectAttempts] = useState(0);
	const maxReconnectAttempts = 3;
	const connectionTimerRef = useRef<NodeJS.Timeout | null>(null);
	const wasActiveRef = useRef<boolean>(initialIsActive);
	const wasCompletedRef = useRef<boolean>(initialIsCompleted);

	// Preparar URL do servidor com protocolo correto
	const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;
	const formattedServerUrl = serverUrl?.startsWith("wss://")
		? serverUrl
		: `wss://${serverUrl}`;

	// Use the appointment status hook for both doctor and patient to get real-time updates
	const appointmentStatus = useAppointmentStatus(appointmentId);

	// Combine passed props with real-time status
	const isActive = appointmentStatus.status?.isActive ?? initialIsActive;
	const isCompleted = appointmentStatus.status?.isCompleted ?? initialIsCompleted;

	// Notify parent component of important status changes
	useEffect(() => {
		// Check if the status actually changed
		if (wasActiveRef.current !== isActive || wasCompletedRef.current !== isCompleted) {
			// Update refs
			wasActiveRef.current = isActive;
			wasCompletedRef.current = isCompleted;

			// Notify parent
			if (onStatusChange) {
				onStatusChange({ isActive, isCompleted });
			}

			// Force disconnect if appointment completed
			if (isCompleted && token) {
				// For patients, we'll disconnect them automatically
				// For doctors, we'll show a message but let them stay connected if they want
				if (userRole === 'PATIENT') {
					setToken(null);
					toast.info("A consulta foi finalizada pelo médico");
				} else {
					toast.info("Você finalizou a consulta");
				}
			}
		}
	}, [isActive, isCompleted, onStatusChange, token, userRole]);

	// Reset error state when active status changes
	useEffect(() => {
		if (isActive) {
			setConnectionError(null);
		}
	}, [isActive]);

	// Função com retry para obter o token
	const getTokenWithRetry = useCallback(async (retries = 3) => {
		for (let i = 0; i < retries; i++) {
			try {
				console.log(`[VideoRoom] Tentativa ${i + 1} de obter token para appointment: ${appointmentId}`);
				const tokenData = await getLiveKitToken(appointmentId);
				
				console.log('[VideoRoom] Token data received:', {
					hasToken: !!tokenData?.token,
					tokenType: typeof tokenData?.token,
					hasUrl: !!tokenData?.url,
					hasRoom: !!tokenData?.room
				});

				if (!tokenData?.token) {
					throw new Error("Token não gerado - resposta vazia ou inválida");
				}

				// Improved token handling to properly handle different token formats
				let cleanToken: string | null = null;

				if (typeof tokenData.token === "string") {
					cleanToken = tokenData.token;
					console.log('[VideoRoom] Token é string, comprimento:', cleanToken.length);
				} else if (tokenData.token && typeof tokenData.token === "object") {
					console.log('[VideoRoom] Token é objeto, tentando extrair valor...');
					// Handle case where token might be a serializable object with value property
					const tokenObj = tokenData.token as any;
					if (tokenObj.value && typeof tokenObj.value === "string") {
						try {
							const parsed = JSON.parse(tokenObj.value);
							cleanToken = typeof parsed === 'string' ? parsed : null;
						} catch {
							// If parsing fails, use the value directly
							cleanToken = tokenObj.value;
						}
					}
				}

				if (!cleanToken) {
					console.error('[VideoRoom] Token inválido após processamento:', {
						originalToken: tokenData.token,
						cleanToken: cleanToken
					});
					throw new Error("Token inválido após processamento");
				}

				console.log('[VideoRoom] Token processado com sucesso, comprimento:', cleanToken.length);
				return cleanToken;
			} catch (err) {
				console.error(`[VideoRoom] Tentativa ${i + 1} falhou:`, {
					error: err instanceof Error ? err.message : 'Erro desconhecido',
					stack: err instanceof Error ? err.stack : undefined,
					appointmentId
				});
				if (i === retries - 1) throw err;
				await new Promise((resolve) => setTimeout(resolve, 1000 * (i + 1)));
			}
		}
		return null; // Add explicit return for TypeScript
	}, [appointmentId]);

	// Handle connection and reconnection
	const connectToRoom = useCallback(async (isReconnect = false) => {
		// Don't attempt to connect if the consultation is completed
		if (isCompleted) {
			// For doctors, we still allow connection even if completed
			if (userRole === "PATIENT") return;
		}

		// Don't connect if not active
		if (!isActive) return;

		try {
			setIsConnecting(true);
			setConnectionError(null);

			console.log(`[VideoRoom] ${isReconnect ? 'Reconectando' : 'Conectando'} à sala de consulta...`, {
				appointmentId,
				isActive,
				isCompleted,
				userRole
			});

			// Set a connection timeout to avoid hanging indefinitely
			connectionTimerRef.current = setTimeout(() => {
				console.error('[VideoRoom] Connection timeout after 20 seconds');
				setIsConnecting(false);
				setConnectionError(new Error("Tempo limite de conexão excedido. Verifique sua conexão de internet."));

				if (onError) {
					onError(new Error("Connection timeout"));
				}
			}, 20000); // 20 seconds timeout

			// Get the token for the room
			const newToken = await getTokenWithRetry();
			if (newToken) {
				console.log('[VideoRoom] Token obtido com sucesso, definindo token...');
				setToken(newToken);
				if (isReconnect) {
					toast.success("Reconectado à sala de consulta");
				}
			} else {
				throw new Error("Não foi possível obter token de conexão");
			}
		} catch (err) {
			console.error("[VideoRoom] Error getting token:", {
				error: err instanceof Error ? err.message : 'Erro desconhecido',
				stack: err instanceof Error ? err.stack : undefined,
				appointmentId,
				isReconnect
			});
			setConnectionError(err instanceof Error ? err : new Error("Erro ao conectar à sala"));

			if (onError) {
				onError(err as Error);
			}
		} finally {
			setIsConnecting(false);
			// Clear the timeout
			if (connectionTimerRef.current) {
				clearTimeout(connectionTimerRef.current);
				connectionTimerRef.current = null;
			}
		}
	}, [appointmentId, isActive, isCompleted, userRole, getTokenWithRetry, onError]);

	// Attempt to reconnect
	const handleReconnect = useCallback(() => {
		if (reconnectAttempts >= maxReconnectAttempts) {
			toast.error("Número máximo de tentativas excedido. Recarregue a página.");
			return;
		}

		setReconnectAttempts(prev => prev + 1);
		connectToRoom(true);
	}, [connectToRoom, reconnectAttempts, maxReconnectAttempts]);

	// Initial connection and reconnection on changes
	useEffect(() => {
		// Disconnect if completed for patients
		if (isCompleted && userRole === "PATIENT") {
			if (token) {
				setToken(null);
				toast.info("A consulta foi finalizada pelo médico");
			}
			return;
		}

		// Don't connect if not active
		if (!isActive) return;

		// Only connect if not already connecting and not already connected
		if (!isConnecting && !token) {
			connectToRoom();
		}

		// Cleanup
		return () => {
			if (connectionTimerRef.current) {
				clearTimeout(connectionTimerRef.current);
				connectionTimerRef.current = null;
			}
		};
	}, [appointmentId, isActive, isCompleted, userRole, connectToRoom, isConnecting, token]);

	if (connectionError) {
		return (
			<Card className="flex h-full flex-col items-center justify-center p-6">
				<AlertCircle className="h-12 w-12 text-destructive mb-4" />
				<h2 className="text-lg font-medium mb-2">Erro de conexão</h2>
				<Alert variant="error" className="mb-4">
					<AlertTitle>Não foi possível conectar à sala</AlertTitle>
					<AlertDescription>{connectionError.message}</AlertDescription>
				</Alert>
				<div className="flex gap-3">
					<Button
						onClick={handleReconnect}
						disabled={reconnectAttempts >= maxReconnectAttempts || isConnecting || isCompleted}
					>
						{isConnecting ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Conectando...
							</>
						) : (
							<>
								<RefreshCcw className="mr-2 h-4 w-4" />
								Tentar novamente ({reconnectAttempts}/{maxReconnectAttempts})
							</>
						)}
					</Button>
					<Button
						variant="outline"
						onClick={() => window.location.reload()}
					>
						Recarregar página
					</Button>
				</div>
			</Card>
		);
	}

	if (isCompleted && userRole === "PATIENT") {
		return (
			<Card className="flex h-full items-center justify-center">
				<div className="text-center">
					<Video className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
					<h2 className="font-medium text-lg">
						Consulta finalizada pelo médico
					</h2>
					<p className="text-sm text-muted-foreground mt-2">
						Você ainda pode acessar o chat para visualizar o histórico e as informações compartilhadas.
					</p>
				</div>
			</Card>
		);
	}

	if (!isActive) {
		return (
			<Card className="flex h-full items-center justify-center">
				<div className="text-center">
					<Video className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
					<h2 className="font-medium text-lg">
						{userRole === "DOCTOR"
							? "Clique para iniciar a videochamada"
							: "Aguardando médico iniciar"}
					</h2>
				</div>
			</Card>
		);
	}

	if (isConnecting || !token) {
		return (
			<Card className="flex h-full items-center justify-center">
				<div className="text-center">
					<Loader2 className="mx-auto h-8 w-8 animate-spin mb-4" />
					<p className="text-muted-foreground">
						{isConnecting ? "Conectando à sala..." : "Preparando conexão..."}
					</p>
				</div>
			</Card>
		);
	}

	return (
		<div className="relative h-full overflow-hidden rounded-lg">
			<LiveKitRoom
				data-lk-theme="default"
				className="h-full"
				style={
					{
						"--lk-bg": "transparent",
						"--lk-control-bg": "var(--background)",
						"--lk-control-border": "var(--border)",
						"--lk-text-color": "var(--foreground)",
						"--lk-video-bg": "var(--muted)",
					} as React.CSSProperties
				}
				token={token}
				serverUrl={formattedServerUrl}
				connect={true}
				video={true}
				audio={true}
				options={{
					adaptiveStream: true,
					dynacast: true,
					publishDefaults: { simulcast: true },
				}}
				onDisconnected={(reason) => {
					console.log('[VideoRoom] LiveKit disconnected:', {
						reason,
						isActive,
						isCompleted,
						reconnectAttempts,
						maxReconnectAttempts
					});
					
					// Only show disconnection message if it wasn't intentional
					// (this may not catch all cases but helps reduce noise)
					if (isActive && !isCompleted) {
						toast.error("Desconectado da sala. Tentando reconectar...");
						// Try to reconnect automatically unless we've reached max attempts
						if (reconnectAttempts < maxReconnectAttempts) {
							setTimeout(() => {
								handleReconnect();
							}, 2000);
						}
					}
				}}
				onError={(err) => {
					console.error("[VideoRoom] LiveKit room error:", {
						error: err instanceof Error ? err.message : 'Erro desconhecido',
						stack: err instanceof Error ? err.stack : undefined,
						appointmentId,
						userRole
					});
					setConnectionError(err);
					if (onError) onError(err);
				}}
			>
				<div className="flex h-full flex-col">
					<div className="flex-1 bg-slate-800">
						<VideoConference />
					</div>
				</div>
			</LiveKitRoom>
		</div>
	);
}
