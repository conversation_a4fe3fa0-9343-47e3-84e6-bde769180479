import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "next/navigation";
import { getLocale } from "next-intl/server";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { CreditCard, Calendar, DollarSign, CheckCircle, AlertCircle } from "lucide-react";
import { PatientSubscriptionService } from "../../../../patient/services/patient-subscription.service";

export default async function AccountSubscriptionsPage() {
  const { user } = await currentUser();
  const locale = await getLocale();

  if (!user) {
    redirect(`/${locale}/auth/login`);
  }

  // Verificar se o usuário é paciente
  if (user.role !== "PATIENT" && user.role !== "USER") {
    redirect(`/${locale}/app/dashboard`);
  }

  // Buscar assinatura ativa do paciente
  let currentSubscription = null;
  try {
    currentSubscription = await PatientSubscriptionService.getActiveSubscription(user.id);
  } catch (error) {
    console.error("Erro ao buscar assinatura:", error);
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Ativa</Badge>;
      case "PAUSED":
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertCircle className="w-3 h-3 mr-1" />Pausada</Badge>;
      case "CANCELED":
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="w-3 h-3 mr-1" />Cancelada</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Minhas Assinaturas</h1>
        <p className="text-sm text-muted-foreground mt-2">
          Gerencie sua assinatura e histórico de pagamentos
        </p>
      </div>

      {!currentSubscription ? (
        /* No Active Subscription */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Nenhuma Assinatura Ativa
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">Você não possui uma assinatura ativa</h3>
              <p className="text-muted-foreground mb-6">
                Assine um de nossos planos para ter acesso completo aos serviços médicos.
              </p>
              <Button asChild>
                <a href="/patient/subscriptions">Ver Planos Disponíveis</a>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Current Subscription */
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Assinatura Atual
              </CardTitle>
              {getStatusBadge(currentSubscription.status)}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Plano</h4>
                <p className="text-lg font-semibold">{currentSubscription.planName}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Valor</h4>
                <p className="text-lg font-semibold flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  R$ {Number(currentSubscription.planPrice).toFixed(2)}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Próxima cobrança</h4>
                <p className="text-sm flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {currentSubscription.nextBillingDate 
                    ? new Date(currentSubscription.nextBillingDate).toLocaleDateString('pt-BR')
                    : 'N/A'
                  }
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Consultas</h4>
                <p className="text-sm">
                  {currentSubscription.consultationsUsed} de {currentSubscription.consultationsIncluded} utilizadas
                </p>
              </div>
            </div>

            <div className="pt-4 border-t">
              <h4 className="font-medium text-sm text-muted-foreground mb-2">Método de Pagamento</h4>
              <p className="text-sm">
                {currentSubscription.paymentMethod || 'Não informado'}
              </p>
            </div>

            <div className="flex gap-2 pt-4">
              <Button variant="outline" size="sm" asChild>
                <a href="/patient/subscriptions">Alterar Plano</a>
              </Button>
              <Button variant="outline" size="sm">
                Gerenciar Pagamento
              </Button>
              <Button variant="destructive" size="sm">
                Cancelar Assinatura
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Pagamentos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Histórico de pagamentos será exibido aqui</p>
            <p className="text-sm">Em desenvolvimento...</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
