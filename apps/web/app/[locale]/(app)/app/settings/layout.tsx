import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { SettingsMenu } from "@saas/settings/components/SettingsMenu";
import { TeamAvatar } from "@shared/components/TeamAvatar";
import { getLocale, getTranslations } from "next-intl/server";
import type { PropsWithChildren } from "react";
import {
	UserCircle,
	Users,
	Building,
	Stethoscope,
	Calendar,
	FileText,
	CreditCard,
} from "lucide-react";

export default async function SettingsLayout({ children }: PropsWithChildren) {
	const locale = await getLocale();
	const t = await getTranslations();
	const { user, team } = await currentUser();

	console.log("SettingsLayout ===>>> user ===>>>", user);

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	// Define os itens do menu baseado na role do usuário
	const getMenuItemsByRole = () => {
		const baseItems = [
			{
				title: "Conta",
				href: "/app/settings/account/general",
				icon: <UserCircle className="w-4 h-4 mr-2" />
			},
		];

		// Apenas pacientes podem ver assinaturas
		if (user.role === "PATIENT" || user.role === "USER") {
			baseItems.push({
				title: "Assinaturas",
				href: "/app/settings/account/subscriptions",
				icon: <CreditCard className="w-4 h-4 mr-2" />
			});
		}

		// Apenas HOSPITAL ou ADMIN podem ver a página de membros
		if (user.role === "HOSPITAL" || user.role === "ADMIN") {
			baseItems.push({
				title: t("settings.team.members.title"),
				href: "/app/settings/team/members",
				icon: <Users className="w-4 h-4 mr-2" />
			},{
				title: "Configurações",
				href: "/app/settings/team/general",
				icon: <Building className="w-4 h-4 mr-2" />
			});
		}

		// Para médicos, adicionar opções específicas
		if (user.role === "DOCTOR") {
			baseItems.push({
				title: "Perfil médico",
				href: "/app/settings/doctor/profile",
				icon: <Stethoscope className="w-4 h-4 mr-2" />
			});
			baseItems.push({
				title: "Agenda",
				href: "/app/settings/doctor/schedule",
				icon: <Calendar className="w-4 h-4 mr-2" />
			});
			baseItems.push({
				title: "Documentos",
				href: "/app/settings/doctor/documents",
				icon: <FileText className="w-4 h-4 mr-2" />
			});
		}

		// Para pacientes, adicionar uma página de perfil médico
		if (user.role === "PATIENT") {
			baseItems.push({
				title: "Perfil do paciente",
				href: "/app/settings/patient/profile",
				icon: <UserCircle className="w-4 h-4 mr-2" />
			});
		}

		return baseItems;
	};

	const menuItems = [
		{
			title: team ? t("settings.team.title") : t("settings.account.title"),
			avatar: team ? <TeamAvatar name={team.name} avatarUrl={team.avatarUrl} /> : <UserCircle className="w-4 h-4" />,
			items: getMenuItemsByRole(),
		},
	];

	return (
		<div className="container max-w-6xl py-8">
			<div className="flex flex-col md:flex-row">
				<div className="w-full md:max-w-[200px]">
					<SettingsMenu menuItems={menuItems} />
				</div>

				<div className="flex-1">{children}</div>
			</div>
		</div>
	);
}
