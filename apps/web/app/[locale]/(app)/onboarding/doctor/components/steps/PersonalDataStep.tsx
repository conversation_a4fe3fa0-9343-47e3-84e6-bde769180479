'use client';

import React, { useState, useEffect } from 'react';
import { MapPin, User, Calendar, Search } from 'lucide-react';

import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';

import { formatCpf, formatCep, validateCpf } from '../../lib/validation';
import { getCepInfo } from '../../lib/api';
import { StepProps } from '../../lib/types';

// ============================================================================
// COMPONENTE PRINCIPAL - VERSÃO ULTRA SIMPLIFICADA
// ============================================================================

export function PersonalDataStep({ data, onDataChange, isLoading, errors }: StepProps) {
  const [isLoadingCep, setIsLoadingCep] = useState(false);
  const [cepError, setCepError] = useState<string>('');

  // Estado local simplificado
  const [formData, setFormData] = useState({
    cpf: data?.cpf || '',
    gender: data?.gender || 'male',
    address: {
      street: data?.address?.street || '',
      number: data?.address?.number || '',
      complement: data?.address?.complement || '',
      neighborhood: data?.address?.neighborhood || '',
      city: data?.address?.city || '',
      state: data?.address?.state || '',
      zipCode: data?.address?.zipCode || ''
    }
  });

  // Atualiza dados quando o formulário muda - removido para evitar loop infinito
  // onDataChange será chamado diretamente nos handlers

  // Auto-preenchimento do CEP
  useEffect(() => {
    const fetchCepData = async () => {
      if (formData.address.zipCode && formData.address.zipCode.length === 8) {
        setIsLoadingCep(true);
        setCepError('');

        try {
          const cepData = await getCepInfo(formData.address.zipCode);
          if (cepData) {
            setFormData(prev => ({
              ...prev,
              address: {
                ...prev.address,
                street: cepData.street,
                neighborhood: cepData.neighborhood,
                city: cepData.city,
                state: cepData.state
              }
            }));
          } else {
            setCepError('CEP não encontrado');
          }
        } catch (error) {
          setCepError('Erro ao buscar CEP');
        } finally {
          setIsLoadingCep(false);
        }
      }
    };

    fetchCepData();
  }, [formData.address.zipCode]);

  const handleInputChange = (field: string, value: string) => {
    let newFormData;

    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      newFormData = {
        ...formData,
        [parent]: {
          ...formData[parent as keyof typeof formData],
          [child]: value
        }
      };
    } else {
      newFormData = {
        ...formData,
        [field]: value
      };
    }

    setFormData(newFormData);
    onDataChange?.(newFormData);
  };

  const handleCpfChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '');
    handleInputChange('cpf', value);
  };

  const handleCepChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '');
    handleInputChange('address.zipCode', value);
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-50 rounded-full mb-4">
          <User className="w-8 h-8 text-blue-600" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Dados Pessoais
        </h1>
        <p className="text-gray-600">
          Complete suas informações pessoais para finalizar o cadastro
        </p>
      </div>

      <div className="space-y-6">
        {/* CPF e Gênero */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Identificação</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="cpf" className="text-sm font-medium text-gray-700">
                  CPF *
                </Label>
                <Input
                  id="cpf"
                  type="text"
                  placeholder="000.000.000-00"
                  value={formatCpf(formData.cpf)}
                  onChange={handleCpfChange}
                />
              </div>

              <div>
                <Label htmlFor="gender" className="text-sm font-medium text-gray-700">
                  Gênero *
                </Label>
                <Select onValueChange={(value) => handleInputChange('gender', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione seu gênero" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Masculino</SelectItem>
                    <SelectItem value="female">Feminino</SelectItem>
                    <SelectItem value="other">Outro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Endereço */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <MapPin className="w-5 h-5 mr-2" />
              Endereço
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="zipCode" className="text-sm font-medium text-gray-700">
                  CEP *
                </Label>
                <div className="relative">
                  <Input
                    id="zipCode"
                    type="text"
                    placeholder="00000-000"
                    value={formatCep(formData.address.zipCode)}
                    onChange={handleCepChange}
                  />
                  {isLoadingCep && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Search className="w-4 h-4 animate-spin text-gray-400" />
                    </div>
                  )}
                </div>
                {cepError && (
                  <p className="text-sm text-red-600 mt-1">{cepError}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="street" className="text-sm font-medium text-gray-700">
                  Rua/Avenida *
                </Label>
                <Input
                  id="street"
                  value={formData.address.street}
                  onChange={(e) => handleInputChange('address.street', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="number" className="text-sm font-medium text-gray-700">
                  Número *
                </Label>
                <Input
                  id="number"
                  value={formData.address.number}
                  onChange={(e) => handleInputChange('address.number', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="complement" className="text-sm font-medium text-gray-700">
                  Complemento
                </Label>
                <Input
                  id="complement"
                  value={formData.address.complement}
                  onChange={(e) => handleInputChange('address.complement', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="neighborhood" className="text-sm font-medium text-gray-700">
                  Bairro *
                </Label>
                <Input
                  id="neighborhood"
                  value={formData.address.neighborhood}
                  onChange={(e) => handleInputChange('address.neighborhood', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="city" className="text-sm font-medium text-gray-700">
                  Cidade *
                </Label>
                <Input
                  id="city"
                  value={formData.address.city}
                  onChange={(e) => handleInputChange('address.city', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="state" className="text-sm font-medium text-gray-700">
                  Estado *
                </Label>
                <Select onValueChange={(value) => handleInputChange('address.state', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o estado" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="AC">Acre</SelectItem>
                    <SelectItem value="AL">Alagoas</SelectItem>
                    <SelectItem value="AP">Amapá</SelectItem>
                    <SelectItem value="AM">Amazonas</SelectItem>
                    <SelectItem value="BA">Bahia</SelectItem>
                    <SelectItem value="CE">Ceará</SelectItem>
                    <SelectItem value="DF">Distrito Federal</SelectItem>
                    <SelectItem value="ES">Espírito Santo</SelectItem>
                    <SelectItem value="GO">Goiás</SelectItem>
                    <SelectItem value="MA">Maranhão</SelectItem>
                    <SelectItem value="MT">Mato Grosso</SelectItem>
                    <SelectItem value="MS">Mato Grosso do Sul</SelectItem>
                    <SelectItem value="MG">Minas Gerais</SelectItem>
                    <SelectItem value="PA">Pará</SelectItem>
                    <SelectItem value="PB">Paraíba</SelectItem>
                    <SelectItem value="PR">Paraná</SelectItem>
                    <SelectItem value="PE">Pernambuco</SelectItem>
                    <SelectItem value="PI">Piauí</SelectItem>
                    <SelectItem value="RJ">Rio de Janeiro</SelectItem>
                    <SelectItem value="RN">Rio Grande do Norte</SelectItem>
                    <SelectItem value="RS">Rio Grande do Sul</SelectItem>
                    <SelectItem value="RO">Rondônia</SelectItem>
                    <SelectItem value="RR">Roraima</SelectItem>
                    <SelectItem value="SC">Santa Catarina</SelectItem>
                    <SelectItem value="SP">São Paulo</SelectItem>
                    <SelectItem value="SE">Sergipe</SelectItem>
                    <SelectItem value="TO">Tocantins</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
