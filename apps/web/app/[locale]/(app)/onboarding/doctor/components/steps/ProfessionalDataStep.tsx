'use client';

import React, { useState } from 'react';
import { Building2, Award } from 'lucide-react';

import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { Textarea } from '@ui/components/textarea';
import { StepProps } from '../../lib/types';

// ============================================================================
// COMPONENTE PRINCIPAL - VERSÃO SIMPLIFICADA
// ============================================================================

export function ProfessionalDataStep({ data, onDataChange, isLoading, errors }: StepProps) {
  // Estado local simplificado
  const [formData, setFormData] = useState({
    workExperience: data?.workExperience || '',
    bio: data?.bio || ''
  });

  const handleInputChange = (field: string, value: string) => {
    const newFormData = {
      ...formData,
      [field]: value
    };
    setFormData(newFormData);
    onDataChange?.(newFormData);
  };

  const getErrorMessage = (fieldName: string) => {
    const fieldErrors = errors[fieldName];
    return fieldErrors && fieldErrors.length > 0 ? fieldErrors[0] : '';
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-50 rounded-full mb-4">
          <Award className="w-8 h-8 text-blue-600" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Dados Profissionais
        </h1>
        <p className="text-gray-600">
          Informações sobre sua formação e experiência médica
        </p>
      </div>

      <div className="space-y-6">


        {/* Experiência Profissional */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="w-5 h-5 text-blue-600" />
              <span>Experiência Profissional</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="workExperience">Anos de Experiência *</Label>
              <Select value={formData.workExperience} onValueChange={(value) => handleInputChange('workExperience', value)}>
                <SelectTrigger className={getErrorMessage('workExperience') ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Selecione sua experiência" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0-1">0-1 anos</SelectItem>
                  <SelectItem value="2-5">2-5 anos</SelectItem>
                  <SelectItem value="6-10">6-10 anos</SelectItem>
                  <SelectItem value="11-15">11-15 anos</SelectItem>
                  <SelectItem value="16-20">16-20 anos</SelectItem>
                  <SelectItem value="20+">Mais de 20 anos</SelectItem>
                </SelectContent>
              </Select>
              {getErrorMessage('workExperience') && (
                <p className="text-sm text-red-500 mt-1">{getErrorMessage('workExperience')}</p>
              )}
            </div>


            <div>
              <Label htmlFor="bio">Biografia Profissional</Label>
              <Textarea
                id="bio"
                placeholder="Conte um pouco sobre sua experiência e especialização..."
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                rows={4}
              />
              <p className="text-sm text-gray-500 mt-1">
                Esta biografia será exibida no seu perfil para os pacientes
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
