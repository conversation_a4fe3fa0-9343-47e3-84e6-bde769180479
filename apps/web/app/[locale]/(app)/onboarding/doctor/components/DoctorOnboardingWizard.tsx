'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { CheckCircle, AlertCircle } from 'lucide-react';

// Components
import { OnboardingHeader } from './OnboardingHeader';
import { StepProgress, CompactStepProgress } from './ui/StepProgress';

// Steps
import { PersonalDataStep } from './steps/PersonalDataStep';
import { ProfessionalDataStep } from './steps/ProfessionalDataStep';
import { SpecialtiesStep } from './steps/SpecialtiesStep';
import { ScheduleStep } from './steps/ScheduleStep';

// Hooks and utilities
import { useOnboardingState } from '../hooks/useOnboardingState';
import {
  OnboardingStepId,
  ONBOARDING_STEPS,
  StepProps
} from '../lib/types';
import { completeOnboarding, saveStep } from '../lib/api';
import { sanitizeOnboardingData } from '../lib/validation';

// ============================================================================
// FUNÇÃO DE MAPEAMENTO DE DADOS
// ============================================================================

function mapStepDataToApiFormat(stepId: string, data: any) {
  switch (stepId) {
    case 'personal':
      return {
        cpf: data.cpf || '',
        gender: data.gender || 'male',
        address: {
          street: data.address?.street || '',
          number: data.address?.number || '',
          complement: data.address?.complement || '',
          neighborhood: data.address?.neighborhood || '',
          city: data.address?.city || '',
          state: data.address?.state || '',
          zipCode: data.address?.zipCode || ''
        }
      };

    case 'professional':
      return {
        workExperience: data.workExperience || '',
        bio: data.bio || ''
      };

    case 'specialties':
      return {
        primarySpecialtyId: data.primarySpecialtyId || '',
        secondarySpecialtyIds: data.secondarySpecialtyIds || []
      };

    case 'schedule':
      return {
        workingHours: (data.workingHours || []).map((hour: any) => ({
          dayOfWeek: getDayOfWeekNumber(hour.dayOfWeek),
          startTime: hour.startTime,
          endTime: hour.endTime,
          isActive: hour.isActive
        })),
        lunchBreak: data.lunchBreak ? {
          startTime: data.lunchBreak.startTime,
          endTime: data.lunchBreak.endTime,
          isActive: data.lunchBreak.isActive
        } : undefined
      };

    default:
      return data;
  }
}

function getDayOfWeekNumber(dayOfWeek: string): number {
  const days = {
    'monday': 0,
    'tuesday': 1,
    'wednesday': 2,
    'thursday': 3,
    'friday': 4,
    'saturday': 5,
    'sunday': 6
  };
  return days[dayOfWeek as keyof typeof days] ?? 0;
}

// ============================================================================
// CONFIGURAÇÃO DOS STEPS
// ============================================================================

const stepComponents: Record<OnboardingStepId, React.ComponentType<StepProps>> = {
  personal: PersonalDataStep,
  professional: ProfessionalDataStep,
  specialties: SpecialtiesStep,
  schedule: ScheduleStep
};

const stepNames = [
  'Dados Pessoais',
  'Dados Profissionais',
  'Especialidades',
  'Horários'
];

// ============================================================================
// INTERFACE DO COMPONENTE
// ============================================================================

interface DoctorOnboardingWizardProps {
  userId?: string;
  existingDoctor?: any;
  onComplete?: (doctorId: string) => void;
  onCancel?: () => void;
}

// ============================================================================
// COMPONENTE PRINCIPAL - VERSÃO ULTRA SIMPLIFICADA
// ============================================================================

export function DoctorOnboardingWizard({
  userId,
  existingDoctor,
  onComplete,
  onCancel
}: DoctorOnboardingWizardProps) {
  const router = useRouter();
  const { state, actions, progress } = useOnboardingState();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // ============================================================================
  // HANDLERS ULTRA SIMPLIFICADOS
  // ============================================================================

  const handleStepDataChange = (data: any) => {
    const currentStepId = ONBOARDING_STEPS[state.currentStep];
    actions.updateStepData(currentStepId, data);
  };

  const handleNext = async () => {
    const currentStepId = ONBOARDING_STEPS[state.currentStep];
    const stepData = state.data[currentStepId];

    // Validação simples
    if (!stepData || Object.keys(stepData).length === 0) {
      toast.error('Por favor, preencha os dados antes de continuar');
      return;
    }

    try {
      actions.setLoading(true);

      // Debug: Log dos dados antes de salvar
      console.log('Dados do step:', currentStepId, stepData);

      // Mapeia os dados para o formato esperado pela API
      const mappedData = mapStepDataToApiFormat(currentStepId, stepData);
      console.log('Dados mapeados:', mappedData);

      await saveStep({
        stepId: currentStepId,
        data: mappedData,
        validateOnly: false
      });

      // Marca o step como completo
      actions.markStepCompleted(currentStepId);

      // Se é o último step, finaliza o onboarding
      if (state.currentStep === ONBOARDING_STEPS.length - 1) {
        await handleComplete();
      } else {
        // Vai para o próximo step
        actions.setCurrentStep(state.currentStep + 1);
      }

    } catch (error: any) {
      console.error('Erro ao salvar step:', error);
      toast.error(error.message || 'Erro ao salvar dados');
    } finally {
      actions.setLoading(false);
    }
  };

  const handlePrevious = () => {
    if (state.currentStep > 0) {
      actions.setCurrentStep(state.currentStep - 1);
    }
  };

  const handleComplete = async () => {
    try {
      setIsSubmitting(true);

      // Salva todos os dados do onboarding
      const sanitizedData = sanitizeOnboardingData(state.data);

      // Finaliza o onboarding via API
      const result = await completeOnboarding({
        data: sanitizedData as any,
        finalValidation: true
      });

      toast.success('Cadastro finalizado com sucesso!', {
        description: 'Seu perfil médico foi criado e está pronto para uso.',
        icon: <CheckCircle className="w-4 h-4" />
      });

      // Callback de sucesso
      if (onComplete) {
        onComplete(result.doctorId);
      } else {
        // Força um refresh da página para atualizar o estado do usuário
        window.location.href = '/app/dashboard';
      }

    } catch (error: any) {
      console.error('Erro ao finalizar onboarding:', error);
      toast.error('Erro ao finalizar cadastro', {
        description: error.message || 'Tente novamente em alguns instantes',
        icon: <AlertCircle className="w-4 h-4" />
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const currentStepId = ONBOARDING_STEPS[state.currentStep];
  const currentStepData = state.data[currentStepId];
  const CurrentStepComponent = stepComponents[currentStepId];

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header com Logo e Progresso */}
      <OnboardingHeader
        currentStep={state.currentStep}
        totalSteps={ONBOARDING_STEPS.length}
        completedSteps={state.completedSteps}
      />

      {/* Progress Indicator - Desktop */}
      <div className="hidden lg:block bg-white border-b border-gray-200 px-6 py-6">
        <div className="max-w-4xl mx-auto">
          <StepProgress
            currentStep={state.currentStep}
            totalSteps={ONBOARDING_STEPS.length}
            completedSteps={state.completedSteps}
            stepNames={stepNames}
          />
        </div>
      </div>

      {/* Progress Indicator - Mobile */}
      <div className="lg:hidden bg-white border-b border-gray-200 px-4 py-4">
        <CompactStepProgress
          currentStep={state.currentStep}
          totalSteps={ONBOARDING_STEPS.length}
          completedSteps={state.completedSteps}
        />
      </div>

             {/* Main Content */}
             <div className="flex-1 flex flex-col pb-20">
               {/* Step Content */}
               <div className="flex-1 px-6 py-8">
                 <div className="max-w-4xl mx-auto">
                   <CurrentStepComponent
                     data={currentStepData}
                     onDataChange={handleStepDataChange}
                     onNext={handleNext}
                     onPrevious={handlePrevious}
                     isLoading={state.isLoading || isSubmitting}
                     errors={state.errors}
                   />
                 </div>
               </div>
             </div>

             {/* Navigation - Fixed Bottom */}
             <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
          <div className="flex justify-between items-center max-w-4xl mx-auto">
            <button
              onClick={handlePrevious}
              disabled={!progress.canGoBack || state.isLoading || isSubmitting}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              ← Anterior
            </button>
            <button
              onClick={handleNext}
              disabled={!progress.canContinue || state.isLoading || isSubmitting}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {state.isLoading || isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isSubmitting ? 'Finalizando...' : 'Salvando...'}
                </>
              ) : (
                'Próximo →'
              )}
            </button>
          </div>
        </div>

      {/* Error Display */}
      {Object.keys(state.errors).length > 0 && (
        <div className="fixed bottom-20 left-4 right-4 lg:bottom-4 lg:left-4 lg:right-auto lg:w-96 z-50">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-red-800">
                  Corrija os seguintes erros:
                </h4>
                <ul className="mt-1 text-sm text-red-700 space-y-1">
                  {Object.entries(state.errors).map(([field, errors]) => (
                    <li key={field}>
                      <strong>{field}:</strong> {errors.join(', ')}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
