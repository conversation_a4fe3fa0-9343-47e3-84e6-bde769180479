import { z } from 'zod';

// ============================================================================
// ENUMS E CONSTANTES
// ============================================================================

export const ONBOARDING_STEPS = [
  'personal',
  'professional',
  'specialties',
  'schedule'
] as const;

export type OnboardingStepId = typeof ONBOARDING_STEPS[number];

export const STEP_STATUS = {
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  ERROR: 'error'
} as const;

export type StepStatus = typeof STEP_STATUS[keyof typeof STEP_STATUS];

// ============================================================================
// SCHEMAS DE VALIDAÇÃO
// ============================================================================

export const PersonalDataSchema = z.object({
  cpf: z.string().length(11, 'CPF deve ter 11 dígitos'),
  gender: z.enum(['male', 'female', 'other']),
  address: z.object({
    street: z.string().min(5, 'Endereço deve ter pelo menos 5 caracteres'),
    number: z.string().min(1, 'Número é obrigatório'),
    complement: z.string().optional(),
    neighborhood: z.string().min(2, 'Bairro é obrigatório'),
    city: z.string().min(2, 'Cidade é obrigatória'),
    state: z.string().length(2, 'Estado deve ter 2 caracteres'),
    zipCode: z.string().length(8, 'CEP deve ter 8 dígitos')
  })
});

export const ProfessionalDataSchema = z.object({
  workExperience: z.string().min(1, 'Experiência profissional é obrigatória'),
  bio: z.string().max(500, 'Biografia deve ter no máximo 500 caracteres').optional()
});

export const SpecialtyDataSchema = z.object({
  primarySpecialtyId: z.string().min(1, 'Especialidade principal é obrigatória'),
  secondarySpecialtyIds: z.array(z.string()).max(3, 'Máximo 3 especialidades secundárias'),
  certifications: z.array(z.object({
    name: z.string(),
    issuingOrganization: z.string(),
    issueDate: z.string(),
    expiryDate: z.string().optional()
  })).optional()
});

export const ScheduleDataSchema = z.object({
  workingHours: z.array(z.object({
    dayOfWeek: z.number().min(0).max(6),
    startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Formato de hora inválido'),
    endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Formato de hora inválido'),
    isActive: z.boolean()
  })),
  consultationDuration: z.number().min(15).max(120).default(30),
  breakBetweenConsultations: z.number().min(0).max(60).default(5),
  lunchBreak: z.object({
    startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    isActive: z.boolean()
  }).optional()
});

// ============================================================================
// INTERFACES PRINCIPAIS
// ============================================================================

export interface OnboardingStep {
  id: OnboardingStepId;
  title: string;
  description: string;
  icon: string;
  component: React.ComponentType<StepProps>;
  validation: z.ZodSchema;
  required: boolean;
  estimatedTime: number; // em minutos
}

export interface StepProps {
  data: any;
  onDataChange: (data: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isLoading: boolean;
  errors: Record<string, string[]>;
}

export interface OnboardingData {
  personal: z.infer<typeof PersonalDataSchema>;
  professional: z.infer<typeof ProfessionalDataSchema>;
  specialties: z.infer<typeof SpecialtyDataSchema>;
  schedule: z.infer<typeof ScheduleDataSchema>;
}

export interface OnboardingState {
  currentStep: number;
  completedSteps: OnboardingStepId[];
  stepStatuses: Record<OnboardingStepId, StepStatus>;
  data: Partial<OnboardingData>;
  errors: Record<string, string[]>;
  isLoading: boolean;
  isDirty: boolean;
  lastSaved: Date | null;
  autoSaveEnabled: boolean;
}

export interface OnboardingProgress {
  currentStep: OnboardingStepId;
  completedSteps: OnboardingStepId[];
  totalSteps: number;
  completionPercentage: number;
  estimatedTimeRemaining: number;
  canContinue: boolean;
  canGoBack: boolean;
}

// ============================================================================
// TIPOS DE API
// ============================================================================

export interface OnboardingApiResponse<T = any> {
  success: boolean;
  data?: T;
  errors?: Record<string, string[]>;
  message?: string;
}

export interface SaveStepRequest {
  stepId: OnboardingStepId;
  data: any;
  validateOnly?: boolean;
}

export interface ValidateStepRequest {
  stepId: OnboardingStepId;
  data: any;
  validateAll?: boolean;
}

export interface OnboardingCompleteRequest {
  data: OnboardingData;
  finalValidation: boolean;
}

// ============================================================================
// TIPOS DE COMPONENTES
// ============================================================================

export interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  completedSteps: OnboardingStepId[];
  stepStatuses: Record<OnboardingStepId, StepStatus>;
}

export interface StepNavigationProps {
  currentStep: number;
  totalSteps: number;
  canGoNext: boolean;
  canGoPrevious: boolean;
  onNext: () => void;
  onPrevious: () => void;
  isLoading: boolean;
}

export interface AutoSaveIndicatorProps {
  isDirty: boolean;
  lastSaved: Date | null;
  isAutoSaving: boolean;
  autoSaveEnabled: boolean;
}

// ============================================================================
// TIPOS DE HOOKS
// ============================================================================

export interface UseOnboardingStateReturn {
  state: OnboardingState;
  actions: {
    setCurrentStep: (step: number) => void;
    updateStepData: (stepId: OnboardingStepId, data: any) => void;
    markStepCompleted: (stepId: OnboardingStepId) => void;
    setErrors: (errors: Record<string, string[]>) => void;
    clearErrors: () => void;
    setLoading: (loading: boolean) => void;
    reset: () => void;
  };
  progress: OnboardingProgress;
}

export interface UseAutoSaveReturn {
  isAutoSaving: boolean;
  lastSaved: Date | null;
  saveNow: () => Promise<void>;
  enableAutoSave: () => void;
  disableAutoSave: () => void;
}

export interface UseStepValidationReturn {
  validateStep: (stepId: OnboardingStepId, data: any) => Promise<boolean>;
  validateAllSteps: (data: OnboardingData) => Promise<boolean>;
  isValidating: boolean;
  validationErrors: Record<string, string[]>;
}

// ============================================================================
// TIPOS AUXILIARES
// ============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type StepDataType<T extends OnboardingStepId> =
  T extends 'personal' ? z.infer<typeof PersonalDataSchema> :
  T extends 'professional' ? z.infer<typeof ProfessionalDataSchema> :
  T extends 'specialties' ? z.infer<typeof SpecialtyDataSchema> :
  T extends 'schedule' ? z.infer<typeof ScheduleDataSchema> :
  any;
