import { ActiveConsultation } from "../../actions/chats/get-active-consultations";

export interface Consultation extends ActiveConsultation {}

export class ConsultationService {
  private baseUrl = '/api';

  async getActiveConsultations(
    userId: string,
    userRole: 'doctor' | 'patient',
    options: { page?: number; limit?: number; status?: string; loadAll?: boolean } = {}
  ): Promise<{ consultations: Consultation[]; total: number; hasMore: boolean }> {
    try {
      const { page = 1, limit = 20, status, loadAll = false } = options;
      console.log(`[ConsultationService] Buscando consultas para userId: ${userId}, role: ${userRole}, page: ${page}, limit: ${limit}, loadAll: ${loadAll}`);

      const params = new URLSearchParams({
        userId,
        role: userRole,
        page: page.toString(),
        limit: limit.toString(),
        ...(status && { status }),
        ...(loadAll && { loadAll: 'true' })
      });

      const response = await fetch(`${this.baseUrl}/consultations?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao buscar consultas');
      }

      if (!result.consultations) {
        console.log("[ConsultationService] Nenhuma consulta encontrada");
        return { consultations: [], total: 0, hasMore: false };
      }

      console.log(`[ConsultationService] ${result.consultations.length} consultas carregadas do banco (página ${page})`);
      return {
        consultations: result.consultations,
        total: result.total || result.consultations.length,
        hasMore: result.hasMore || false
      };

    } catch (error) {
      console.error('[ConsultationService] Failed to fetch consultations:', error);
      throw new Error(`Failed to fetch consultations: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async startConsultation(consultationId: string, userId: string): Promise<void> {
    try {
      console.log(`[ConsultationService] Iniciando consulta: ${consultationId}`);

      const response = await fetch(`${this.baseUrl}/appointments/${consultationId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'IN_PROGRESS',
          userId: userId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success || result.error) {
        throw new Error(result.error || 'Failed to start consultation');
      }

      console.log(`[ConsultationService] Consulta ${consultationId} iniciada com sucesso`);

    } catch (error) {
      console.error('[ConsultationService] Failed to start consultation:', error);
      throw new Error(`Failed to start consultation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async endConsultation(consultationId: string, userId: string): Promise<void> {
    try {
      console.log(`[ConsultationService] Finalizando consulta: ${consultationId}`);

      const response = await fetch(`${this.baseUrl}/appointments/${consultationId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'COMPLETED',
          userId: userId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success || result.error) {
        throw new Error(result.error || 'Failed to end consultation');
      }

      console.log(`[ConsultationService] Consulta ${consultationId} finalizada com sucesso`);

    } catch (error) {
      console.error('[ConsultationService] Failed to end consultation:', error);
      throw new Error(`Failed to end consultation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getConsultationById(consultationId: string): Promise<Consultation | null> {
    try {
      console.log(`[ConsultationService] Buscando consulta por ID: ${consultationId}`);

      const response = await fetch(`${this.baseUrl}/appointments/${consultationId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success || result.error) {
        throw new Error(result.error || 'Failed to fetch consultation');
      }

      console.log(`[ConsultationService] Consulta encontrada:`, result.consultation);
      return result.consultation;

    } catch (error) {
      console.error('[ConsultationService] Failed to fetch consultation:', error);
      throw new Error(`Failed to fetch consultation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
