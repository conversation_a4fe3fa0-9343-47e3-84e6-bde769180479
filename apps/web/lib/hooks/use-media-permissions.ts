"use client";

import { useState, useCallback, useEffect } from "react";

export interface MediaPermissionState {
  camera: 'unknown' | 'granted' | 'denied' | 'prompt';
  microphone: 'unknown' | 'granted' | 'denied' | 'prompt';
  isSupported: boolean;
  isLoading: boolean;
}

export interface MediaPermissionError {
  name: string;
  message: string;
  type: 'permission' | 'device' | 'security' | 'browser' | 'unknown';
}

export function useMediaPermissions() {
  const [state, setState] = useState<MediaPermissionState>({
    camera: 'unknown',
    microphone: 'unknown',
    isSupported: false,
    isLoading: false
  });

  const [error, setError] = useState<MediaPermissionError | null>(null);

  // Verificar se o navegador suporta a API de permissões
  const checkSupport = useCallback(() => {
    const isSupported = !!(
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia &&
      navigator.permissions &&
      navigator.permissions.query
    );
    
    setState(prev => ({ ...prev, isSupported }));
    return isSupported;
  }, []);

  // Verificar status atual das permissões
  const checkPermissions = useCallback(async () => {
    if (!state.isSupported) {
      setError({
        name: 'NotSupportedError',
        message: 'Navegador não suporta verificação de permissões',
        type: 'browser'
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true }));
    setError(null);

    try {
      const [cameraPermission, micPermission] = await Promise.all([
        navigator.permissions.query({ name: 'camera' as PermissionName }).catch(() => null),
        navigator.permissions.query({ name: 'microphone' as PermissionName }).catch(() => null)
      ]);

      setState(prev => ({
        ...prev,
        camera: cameraPermission?.state || 'unknown',
        microphone: micPermission?.state || 'unknown',
        isLoading: false
      }));
    } catch (err) {
      console.error('Erro ao verificar permissões:', err);
      setError({
        name: 'PermissionCheckError',
        message: 'Não foi possível verificar o status das permissões',
        type: 'unknown'
      });
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [state.isSupported]);

  // Solicitar permissões de mídia com fallback automático
  const requestPermissions = useCallback(async (options: {
    audio?: boolean;
    video?: boolean;
    constraints?: MediaStreamConstraints;
  } = {}) => {
    const { audio = true, video = false, constraints } = options;
    
    setState(prev => ({ ...prev, isLoading: true }));
    setError(null);

    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Navegador não suporta acesso à mídia');
      }

      const defaultConstraints: MediaStreamConstraints = {
        audio: audio ? {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } : false,
        video: video ? {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        } : false
      };

      const finalConstraints = constraints || defaultConstraints;
      
      console.log('🎤 Solicitando permissões com constraints:', finalConstraints);
      
      const stream = await navigator.mediaDevices.getUserMedia(finalConstraints);
      
      console.log('✅ Permissões concedidas:', {
        audioTracks: stream.getAudioTracks().length,
        videoTracks: stream.getVideoTracks().length
      });

      // Parar o stream imediatamente
      stream.getTracks().forEach(track => {
        console.log('🛑 Parando track:', track.kind, track.label);
        track.stop();
      });

      // Atualizar status das permissões
      await checkPermissions();
      
      setState(prev => ({ ...prev, isLoading: false }));
      return true;
    } catch (err: any) {
      console.error('❌ Erro ao solicitar permissões:', err);
      
      // Tentar fallback com configurações mais simples
      if (err.name === 'OverconstrainedError' && !constraints) {
        console.log('⚠️ Tentando fallback com configurações básicas...');
        try {
          const basicConstraints: MediaStreamConstraints = {
            audio: audio ? true : false,
            video: video ? true : false
          };
          
          const basicStream = await navigator.mediaDevices.getUserMedia(basicConstraints);
          
          console.log('✅ Permissões concedidas com fallback:', {
            audioTracks: basicStream.getAudioTracks().length,
            videoTracks: basicStream.getVideoTracks().length
          });

          basicStream.getTracks().forEach(track => track.stop());
          await checkPermissions();
          
          setState(prev => ({ ...prev, isLoading: false }));
          return true;
        } catch (fallbackErr: any) {
          console.error('❌ Fallback também falhou:', fallbackErr);
          err = fallbackErr; // Usar o erro do fallback
        }
      }
      
      let errorType: MediaPermissionError['type'] = 'unknown';
      let errorMessage = 'Erro ao acessar dispositivos de mídia';

      switch (err.name) {
        case 'NotAllowedError':
          errorType = 'permission';
          errorMessage = 'Permissão de microfone/câmera negada. Clique no ícone de câmera/microfone na barra de endereços do navegador e permita o acesso.';
          break;
        case 'NotFoundError':
          errorType = 'device';
          errorMessage = 'Dispositivo de microfone/câmera não encontrado. Verifique se os dispositivos estão conectados.';
          break;
        case 'NotReadableError':
          errorType = 'device';
          errorMessage = 'Dispositivo de mídia está sendo usado por outro aplicativo. Feche outros aplicativos e tente novamente.';
          break;
        case 'OverconstrainedError':
          errorType = 'device';
          errorMessage = 'Configurações de mídia não suportadas pelo dispositivo.';
          break;
        case 'SecurityError':
          errorType = 'security';
          errorMessage = 'Erro de segurança ao acessar mídia. Certifique-se de que está usando HTTPS ou localhost.';
          break;
        case 'TypeError':
          errorType = 'browser';
          errorMessage = 'Navegador não suporta acesso à mídia. Use um navegador moderno.';
          break;
      }

      setError({
        name: err.name,
        message: errorMessage,
        type: errorType
      });

      setState(prev => ({ ...prev, isLoading: false }));
      return false;
    }
  }, [checkPermissions]);

  // Tentar com configurações mais básicas em caso de erro
  const requestBasicPermissions = useCallback(async (audio = true, video = false) => {
    return requestPermissions({
      audio,
      video,
      constraints: {
        audio: audio ? true : false,
        video: video ? true : false
      }
    });
  }, [requestPermissions]);

  // Verificar suporte e permissões na inicialização
  useEffect(() => {
    checkSupport();
  }, [checkSupport]);

  // Verificar permissões quando o suporte for confirmado
  useEffect(() => {
    if (state.isSupported) {
      checkPermissions();
    }
  }, [state.isSupported, checkPermissions]);

  return {
    ...state,
    error,
    checkPermissions,
    requestPermissions,
    requestBasicPermissions,
    checkSupport
  };
}
