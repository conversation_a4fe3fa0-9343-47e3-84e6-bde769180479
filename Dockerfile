FROM node:20-slim AS base

RUN apt-get update && apt-get install -y \
    openssl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

RUN npm install -g pnpm@9.3.0

WORKDIR /app

COPY package.json pnpm-workspace.yaml pnpm-lock.yaml turbo.json ./

COPY apps/web/package.json ./apps/web/
COPY packages/api/package.json ./packages/api/
COPY packages/auth/package.json ./packages/auth/
COPY packages/database/package.json ./packages/database/
COPY packages/i18n/package.json ./packages/i18n/
COPY packages/logs/package.json ./packages/logs/
COPY packages/mail/package.json ./packages/mail/
COPY packages/storage/package.json ./packages/storage/
COPY packages/utils/package.json ./packages/utils/
COPY tooling/tailwind/package.json ./tooling/tailwind/
COPY tooling/typescript/package.json ./tooling/typescript/

FROM base AS deps
RUN pnpm install --frozen-lockfile

FROM deps AS db-generate
COPY packages/database ./packages/database
RUN pnpm --filter database run db:generate

FROM db-generate AS builder
COPY . .

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

ENV NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL:-https://moupvfqlulvqbzwajkif.supabase.co}
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MFeB-phlRJBVc_a2ZeS-yUP6LOLc9C0L4jF6BIqv0i0}
ENV NEXT_PUBLIC_SITE_URL=${NEXT_PUBLIC_SITE_URL:-https://zapvida.com}
ENV NEXT_PUBLIC_S3_ENDPOINT=${NEXT_PUBLIC_S3_ENDPOINT:-https://moupvfqlulvqbzwajkif.supabase.co/storage/v1/s3}
ENV NEXT_PUBLIC_AVATARS_BUCKET_NAME=${NEXT_PUBLIC_AVATARS_BUCKET_NAME:-avatars}
ENV NEXT_PUBLIC_UPLOADS_BUCKET_NAME=${NEXT_PUBLIC_UPLOADS_BUCKET_NAME:-medical_docs}
ENV NEXT_PUBLIC_CHAT_ATTACHMENTS_BUCKET=${NEXT_PUBLIC_CHAT_ATTACHMENTS_BUCKET:-chat_attachments}
ENV NEXT_PUBLIC_LIVEKIT_URL=${NEXT_PUBLIC_LIVEKIT_URL:-dev-gmyd2e9t.livekit.cloud}
ENV NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=${NEXT_PUBLIC_GOOGLE_ANALYTICS_ID:-AW-347192758}
ENV NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY:-phc_BU1u3gA1nCjXQNTQJQBhhOVyaq6oEMQNzZ4HFeLflYO}
ENV NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST:-https://us.i.posthog.com}
ENV NEXT_PUBLIC_ZAPCHAT_V2_ENABLED=${NEXT_PUBLIC_ZAPCHAT_V2_ENABLED:-true}
ENV NEXT_PUBLIC_ZAPCHAT_V2_BETA=${NEXT_PUBLIC_ZAPCHAT_V2_BETA:-true}

RUN pnpm --filter database run db:generate

RUN pnpm --filter web run build

FROM node:20-slim AS runner
RUN apt-get update && apt-get install -y \
    openssl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

RUN groupadd --system --gid 1001 nodejs
RUN useradd --system --uid 1001 nextjs

RUN npm install -g pnpm@9.3.0

WORKDIR /app

COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/node_modules ./apps/web/node_modules
COPY --from=builder --chown=nextjs:nodejs /app/packages ./packages
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./
COPY --from=builder --chown=nextjs:nodejs /app/pnpm-workspace.yaml ./
COPY --from=builder --chown=nextjs:nodejs /app/turbo.json ./
USER nextjs

EXPOSE 3000

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

CMD ["node", "apps/web/server.js"]