import { z } from 'zod';
import { Prisma } from '@prisma/client';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////

// JSON
//------------------------------------------------------

export type NullableJsonInput = Prisma.JsonValue | null | 'JsonNull' | 'DbNull' | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;

export const transformJsonNull = (v?: NullableJsonInput) => {
  if (!v || v === 'DbNull') return Prisma.DbNull;
  if (v === 'JsonNull') return Prisma.JsonNull;
  return v;
};

export const JsonValueSchema: z.ZodType<Prisma.JsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.literal(null),
    z.record(z.lazy(() => JsonValueSchema.optional())),
    z.array(z.lazy(() => JsonValueSchema)),
  ])
);

export type JsonValueType = z.infer<typeof JsonValueSchema>;

export const NullableJsonValue = z
  .union([JsonValueSchema, z.literal('DbNull'), z.literal('JsonNull')])
  .nullable()
  .transform((v) => transformJsonNull(v));

export type NullableJsonValueType = z.infer<typeof NullableJsonValue>;

export const InputJsonValueSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.object({ toJSON: z.function(z.tuple([]), z.any()) }),
    z.record(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
    z.array(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
  ])
);

export type InputJsonValueType = z.infer<typeof InputJsonValueSchema>;

// DECIMAL
//------------------------------------------------------

export const DecimalJsLikeSchema: z.ZodType<Prisma.DecimalJsLike> = z.object({
  d: z.array(z.number()),
  e: z.number(),
  s: z.number(),
  toFixed: z.function(z.tuple([]), z.string()),
})

export const DECIMAL_STRING_REGEX = /^(?:-?Infinity|NaN|-?(?:0[bB][01]+(?:\.[01]+)?(?:[pP][-+]?\d+)?|0[oO][0-7]+(?:\.[0-7]+)?(?:[pP][-+]?\d+)?|0[xX][\da-fA-F]+(?:\.[\da-fA-F]+)?(?:[pP][-+]?\d+)?|(?:\d+|\d*\.\d+)(?:[eE][-+]?\d+)?))$/;

export const isValidDecimalInput =
  (v?: null | string | number | Prisma.DecimalJsLike): v is string | number | Prisma.DecimalJsLike => {
    if (v === undefined || v === null) return false;
    return (
      (typeof v === 'object' && 'd' in v && 'e' in v && 's' in v && 'toFixed' in v) ||
      (typeof v === 'string' && DECIMAL_STRING_REGEX.test(v)) ||
      typeof v === 'number'
    )
  };

/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const TransactionIsolationLevelSchema = z.enum(['ReadUncommitted','ReadCommitted','RepeatableRead','Serializable']);

export const UserScalarFieldEnumSchema = z.enum(['id','email','emailVerified','role','name','avatarUrl','createdAt','hashedPassword','onboardingComplete','gatewayCustomerId','phone']);

export const UserSessionScalarFieldEnumSchema = z.enum(['id','userId','expiresAt','impersonatorId']);

export const UserOauthAccountScalarFieldEnumSchema = z.enum(['id','providerId','providerUserId','userId']);

export const UserVerificationTokenScalarFieldEnumSchema = z.enum(['id','userId','expires']);

export const UserOneTimePasswordScalarFieldEnumSchema = z.enum(['id','userId','code','type','identifier','expires']);

export const TeamScalarFieldEnumSchema = z.enum(['id','name','avatarUrl','teamType','metadata']);

export const TeamMembershipScalarFieldEnumSchema = z.enum(['id','teamId','userId','role','isCreator']);

export const TeamInvitationScalarFieldEnumSchema = z.enum(['id','teamId','email','role','createdAt','expiresAt']);

export const SubscriptionScalarFieldEnumSchema = z.enum(['id','teamId','customerId','status','planId','variantId','nextPaymentDate']);

export const HospitalScalarFieldEnumSchema = z.enum(['id','teamId','name','cnpj','contactEmail','contactPhone','address','logoUrl','settings','createdAt','updatedAt']);

export const DepartmentScalarFieldEnumSchema = z.enum(['id','hospitalId','teamId','name','description','isActive','createdAt','updatedAt']);

export const DoctorScalarFieldEnumSchema = z.enum(['id','userId','crm','crmState','biography','consultationPrice','consultationDuration','returnPeriod','memedToken','documentStatus','onlineStatus','rating','totalRatings','isAvailableForOnDuty','onDutyPriceMultiplier','maxConcurrentOnDuty','scheduleConfig','consultTypes','customQuestions','bankAccount','asaasId','createdAt','updatedAt']);

export const PatientScalarFieldEnumSchema = z.enum(['id','userId','cpf','birthDate','gender','maritalStatus','occupation','address','bloodType','height','weight','allergies','chronicConditions','medications','surgeries','familyHistory','emergencyContact','healthInsurance','communicationPreferences','hasActiveSubscription','createdAt','updatedAt']);

export const MedicalDocumentScalarFieldEnumSchema = z.enum(['id','patientId','name','type','fileUrl','uploadedAt']);

export const SpecialtyScalarFieldEnumSchema = z.enum(['id','name','description','searchCount','createdAt']);

export const DoctorHospitalScalarFieldEnumSchema = z.enum(['id','doctorId','hospitalId','departmentId','isActive','createdAt','updatedAt']);

export const PreAnestheticFormScalarFieldEnumSchema = z.enum(['id','appointmentId','hospitalId','templateId','patientData','clinicalEvaluation','physicalExam','labTests','conclusion','status','signatureData','signedAt','signedBy','version','history','createdAt','updatedAt']);

export const PreAnestheticTemplateScalarFieldEnumSchema = z.enum(['id','hospitalId','name','description','fields','isActive','createdAt','updatedAt']);

export const DigitalCertificateScalarFieldEnumSchema = z.enum(['id','doctorId','type','metadata','validUntil','isActive','createdAt','updatedAt']);

export const AppointmentScalarFieldEnumSchema = z.enum(['id','doctorId','patientId','hospitalId','scheduledAt','consultType','duration','status','symptoms','preConsultForm','appointmentType','isOnDuty','urgencyLevel','queuePosition','acceptedAt','acceptedByDoctorId','roomId','chatEnabled','recordingEnabled','amount','paymentStatus','paymentId','transactionId','isReturn','previousAppointmentId','partnerSource','createdAt','updatedAt']);

export const DoctorScheduleScalarFieldEnumSchema = z.enum(['id','doctorId','weekDay','startTime','endTime','isEnabled','isBreak','createdAt','updatedAt']);

export const ScheduleBlockScalarFieldEnumSchema = z.enum(['id','doctorId','startTime','endTime','reason','type','description','createdAt','updatedAt']);

export const TimeSlotScalarFieldEnumSchema = z.enum(['id','doctorId','startTime','endTime','isAvailable','appointmentId','createdAt','updatedAt']);

export const TransactionScalarFieldEnumSchema = z.enum(['id','appointmentId','patientSubscriptionId','amount','platformFee','doctorAmount','status','paymentMethod','asaasId','dueDate','paidAt','refundedAt','doctorId','hospitalId','partnerSource','createdAt','updatedAt']);

export const InvoiceScalarFieldEnumSchema = z.enum(['id','transactionId','number','series','url','issuedAt','metadata','createdAt']);

export const PrescriptionScalarFieldEnumSchema = z.enum(['id','appointmentId','memedId','content','pdfUrl','status','createdAt','updatedAt']);

export const DoctorEvaluationScalarFieldEnumSchema = z.enum(['id','doctorId','patientId','appointmentId','rating','comment','createdAt']);

export const DoctorDocumentScalarFieldEnumSchema = z.enum(['id','doctorId','type','fileName','fileUrl','status','notes','reviewedAt','createdAt','updatedAt']);

export const MessageScalarFieldEnumSchema = z.enum(['id','appointmentId','senderId','type','content','metadata','senderRole','createdAt']);

export const NotificationScalarFieldEnumSchema = z.enum(['id','userId','appointmentId','type','title','message','read','createdAt']);

export const AttachmentScalarFieldEnumSchema = z.enum(['id','appointmentId','name','url','type','size','uploadedBy','createdAt']);

export const AppointmentNoteScalarFieldEnumSchema = z.enum(['id','appointmentId','doctorId','content','createdAt','updatedAt']);

export const MedicalRecordScalarFieldEnumSchema = z.enum(['id','appointmentId','main_complaint','diagnosis','conduct','cid_codes','cid_descriptions','createdAt','updatedAt']);

export const CommissionSettingsScalarFieldEnumSchema = z.enum(['id','platformFeeRate','minFeeAmount','maxFeeAmount','isActive','createdAt','updatedAt']);

export const PatientSubscriptionScalarFieldEnumSchema = z.enum(['id','patientId','planId','planName','planPrice','asaasSubscriptionId','status','startDate','endDate','nextBillingDate','currentPeriodStart','currentPeriodEnd','cycle','gracePeriodDays','retryCount','maxRetryCount','overdueSince','unpaidSince','lastPaymentStatus','lastPaymentAt','failedPaymentAt','cancelAtPeriodEnd','pausedAt','canceledAt','renewalCount','paymentMethod','externalCustomerId','metadata','consultationsIncluded','consultationsUsed','lastResetDate','autoRenewal','consultationsUsedThisPeriod','periodStartDate','periodEndDate','lastNotificationAt','notificationCount','createdAt','updatedAt']);

export const ConsultationUsageScalarFieldEnumSchema = z.enum(['id','patientId','subscriptionId','appointmentId','usedAt','type','doctorId','amount','commission','createdAt']);

export const SubscriptionNotificationScalarFieldEnumSchema = z.enum(['id','subscriptionId','type','title','message','sentAt','readAt']);

export const DoctorWithdrawalScalarFieldEnumSchema = z.enum(['id','doctorId','amount','status','requestedAt','processedAt','bankAccount','notes']);

export const SecurePaymentDataScalarFieldEnumSchema = z.enum(['id','transactionId','paymentToken','tokenIv','tokenTag','paymentHash','lastFourDigits','cardBrand','cardType','encryptionVersion','createdAt','updatedAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const NullableJsonNullValueInputSchema = z.enum(['DbNull','JsonNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.DbNull : value);

export const JsonNullValueInputSchema = z.enum(['JsonNull',]).transform((value) => (value === 'JsonNull' ? Prisma.JsonNull : value));

export const QueryModeSchema = z.enum(['default','insensitive']);

export const NullsOrderSchema = z.enum(['first','last']);

export const JsonNullValueFilterSchema = z.enum(['DbNull','JsonNull','AnyNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.JsonNull : value === 'AnyNull' ? Prisma.AnyNull : value);

export const UserRoleSchema = z.enum(['USER','ADMIN','DOCTOR','PATIENT','SECRETARY','HOSPITAL']);

export type UserRoleType = `${z.infer<typeof UserRoleSchema>}`

export const UserOneTimePasswordTypeSchema = z.enum(['SIGNUP','LOGIN','PASSWORD_RESET']);

export type UserOneTimePasswordTypeType = `${z.infer<typeof UserOneTimePasswordTypeSchema>}`

export const TeamMemberRoleSchema = z.enum(['MEMBER','OWNER','DOCTOR','SECRETARY','ADMIN']);

export type TeamMemberRoleType = `${z.infer<typeof TeamMemberRoleSchema>}`

export const SubscriptionStatusSchema = z.enum(['TRIALING','ACTIVE','PAUSED','CANCELED','PAST_DUE','UNPAID','INCOMPLETE','EXPIRED']);

export type SubscriptionStatusType = `${z.infer<typeof SubscriptionStatusSchema>}`

export const DoctorStatusSchema = z.enum(['PENDING','APPROVED','REJECTED']);

export type DoctorStatusType = `${z.infer<typeof DoctorStatusSchema>}`

export const OnlineStatusSchema = z.enum(['ONLINE','OFFLINE','BUSY']);

export type OnlineStatusType = `${z.infer<typeof OnlineStatusSchema>}`

export const ConsultTypeSchema = z.enum(['VIDEO','AUDIO','CHAT']);

export type ConsultTypeType = `${z.infer<typeof ConsultTypeSchema>}`

export const AppointmentStatusSchema = z.enum(['SCHEDULED','IN_PROGRESS','COMPLETED','CANCELED','NO_SHOW','WAITING_ON_DUTY','ACCEPTED_BY_DOCTOR']);

export type AppointmentStatusType = `${z.infer<typeof AppointmentStatusSchema>}`

export const PaymentStatusSchema = z.enum(['PENDING','PAID','REFUNDED','FAILED']);

export type PaymentStatusType = `${z.infer<typeof PaymentStatusSchema>}`

export const PaymentMethodSchema = z.enum(['CREDIT_CARD','BOLETO','PIX']);

export type PaymentMethodType = `${z.infer<typeof PaymentMethodSchema>}`

export const NotificationTypeSchema = z.enum(['APPOINTMENT_CREATED','APPOINTMENT_REMINDER','APPOINTMENT_CANCELED','PRESCRIPTION_READY','DOCTOR_APPROVED','PAYMENT_CONFIRMED']);

export type NotificationTypeType = `${z.infer<typeof NotificationTypeSchema>}`

export const MessageTypeSchema = z.enum(['TEXT','AUDIO','FILE','SYSTEM']);

export type MessageTypeType = `${z.infer<typeof MessageTypeSchema>}`

export const BlockTypeSchema = z.enum(['VACATION','HOLIDAY','LUNCH','PERSONAL','OTHER']);

export type BlockTypeType = `${z.infer<typeof BlockTypeSchema>}`

export const DocumentStatusSchema = z.enum(['PENDING','APPROVED','REJECTED']);

export type DocumentStatusType = `${z.infer<typeof DocumentStatusSchema>}`

export const DocumentTypeSchema = z.enum(['CRM','SPECIALTY','ID','COLLEGE_DEGREE','RESIDENCY','OTHER']);

export type DocumentTypeType = `${z.infer<typeof DocumentTypeSchema>}`

export const AppointmentTypeSchema = z.enum(['TELEMEDICINE','AMBULATORY','PRE_ANESTHETIC']);

export type AppointmentTypeType = `${z.infer<typeof AppointmentTypeSchema>}`

export const UrgencyLevelSchema = z.enum(['HIGH','MEDIUM','LOW']);

export type UrgencyLevelType = `${z.infer<typeof UrgencyLevelSchema>}`

export const PatientSubscriptionStatusSchema = z.enum(['ACTIVE','CANCELED','PAST_DUE','UNPAID','PAUSED']);

export type PatientSubscriptionStatusType = `${z.infer<typeof PatientSubscriptionStatusSchema>}`

export const ConsultationUsageTypeSchema = z.enum(['SUBSCRIPTION','SINGLE_PAYMENT','ON_DUTY']);

export type ConsultationUsageTypeType = `${z.infer<typeof ConsultationUsageTypeSchema>}`

export const SubscriptionCycleSchema = z.enum(['WEEKLY','BIWEEKLY','MONTHLY','QUARTERLY','SEMIANNUALLY','YEARLY']);

export type SubscriptionCycleType = `${z.infer<typeof SubscriptionCycleSchema>}`

/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  role: UserRoleSchema,
  id: z.string().cuid(),
  email: z.string(),
  emailVerified: z.boolean(),
  name: z.string().nullable(),
  avatarUrl: z.string().nullable(),
  createdAt: z.coerce.date(),
  hashedPassword: z.string().nullable(),
  onboardingComplete: z.boolean(),
  gatewayCustomerId: z.string().nullable(),
  phone: z.string().nullable(),
})

export type User = z.infer<typeof UserSchema>

/////////////////////////////////////////
// USER SESSION SCHEMA
/////////////////////////////////////////

export const UserSessionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  expiresAt: z.coerce.date(),
  impersonatorId: z.string().nullable(),
})

export type UserSession = z.infer<typeof UserSessionSchema>

/////////////////////////////////////////
// USER OAUTH ACCOUNT SCHEMA
/////////////////////////////////////////

export const UserOauthAccountSchema = z.object({
  id: z.string().cuid(),
  providerId: z.string(),
  providerUserId: z.string(),
  userId: z.string(),
})

export type UserOauthAccount = z.infer<typeof UserOauthAccountSchema>

/////////////////////////////////////////
// USER VERIFICATION TOKEN SCHEMA
/////////////////////////////////////////

export const UserVerificationTokenSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  expires: z.coerce.date(),
})

export type UserVerificationToken = z.infer<typeof UserVerificationTokenSchema>

/////////////////////////////////////////
// USER ONE TIME PASSWORD SCHEMA
/////////////////////////////////////////

export const UserOneTimePasswordSchema = z.object({
  type: UserOneTimePasswordTypeSchema,
  id: z.string().cuid(),
  userId: z.string(),
  code: z.string(),
  identifier: z.string(),
  expires: z.coerce.date(),
})

export type UserOneTimePassword = z.infer<typeof UserOneTimePasswordSchema>

/////////////////////////////////////////
// TEAM SCHEMA
/////////////////////////////////////////

export const TeamSchema = z.object({
  id: z.string().cuid(),
  name: z.string(),
  avatarUrl: z.string().nullable(),
  teamType: z.string().nullable(),
  metadata: JsonValueSchema.nullable(),
})

export type Team = z.infer<typeof TeamSchema>

/////////////////////////////////////////
// TEAM MEMBERSHIP SCHEMA
/////////////////////////////////////////

export const TeamMembershipSchema = z.object({
  role: TeamMemberRoleSchema,
  id: z.string().cuid(),
  teamId: z.string(),
  userId: z.string(),
  isCreator: z.boolean(),
})

export type TeamMembership = z.infer<typeof TeamMembershipSchema>

/////////////////////////////////////////
// TEAM INVITATION SCHEMA
/////////////////////////////////////////

export const TeamInvitationSchema = z.object({
  role: TeamMemberRoleSchema,
  id: z.string().cuid(),
  teamId: z.string(),
  email: z.string(),
  createdAt: z.coerce.date(),
  expiresAt: z.coerce.date(),
})

export type TeamInvitation = z.infer<typeof TeamInvitationSchema>

/////////////////////////////////////////
// SUBSCRIPTION SCHEMA
/////////////////////////////////////////

export const SubscriptionSchema = z.object({
  status: SubscriptionStatusSchema,
  id: z.string(),
  teamId: z.string(),
  customerId: z.string(),
  planId: z.string(),
  variantId: z.string(),
  nextPaymentDate: z.coerce.date().nullable(),
})

export type Subscription = z.infer<typeof SubscriptionSchema>

/////////////////////////////////////////
// HOSPITAL SCHEMA
/////////////////////////////////////////

export const HospitalSchema = z.object({
  id: z.string().cuid(),
  teamId: z.string(),
  name: z.string(),
  cnpj: z.string(),
  contactEmail: z.string(),
  contactPhone: z.string(),
  address: JsonValueSchema,
  logoUrl: z.string().nullable(),
  settings: JsonValueSchema,
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Hospital = z.infer<typeof HospitalSchema>

/////////////////////////////////////////
// DEPARTMENT SCHEMA
/////////////////////////////////////////

export const DepartmentSchema = z.object({
  id: z.string().cuid(),
  hospitalId: z.string(),
  teamId: z.string().nullable(),
  name: z.string(),
  description: z.string().nullable(),
  isActive: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Department = z.infer<typeof DepartmentSchema>

/////////////////////////////////////////
// DOCTOR SCHEMA
/////////////////////////////////////////

export const DoctorSchema = z.object({
  documentStatus: DoctorStatusSchema,
  onlineStatus: OnlineStatusSchema,
  consultTypes: ConsultTypeSchema.array(),
  id: z.string().cuid(),
  userId: z.string(),
  crm: z.string(),
  crmState: z.string(),
  biography: z.string().nullable(),
  consultationPrice: z.instanceof(Prisma.Decimal, { message: "Field 'consultationPrice' must be a Decimal. Location: ['Models', 'Doctor']"}),
  consultationDuration: z.number().int(),
  returnPeriod: z.number().int(),
  memedToken: z.string().nullable(),
  rating: z.number().nullable(),
  totalRatings: z.number().int(),
  isAvailableForOnDuty: z.boolean(),
  onDutyPriceMultiplier: z.instanceof(Prisma.Decimal, { message: "Field 'onDutyPriceMultiplier' must be a Decimal. Location: ['Models', 'Doctor']"}).nullable(),
  maxConcurrentOnDuty: z.number().int(),
  scheduleConfig: JsonValueSchema.nullable(),
  customQuestions: JsonValueSchema.nullable(),
  bankAccount: JsonValueSchema.nullable(),
  asaasId: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Doctor = z.infer<typeof DoctorSchema>

/////////////////////////////////////////
// PATIENT SCHEMA
/////////////////////////////////////////

export const PatientSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  cpf: z.string(),
  birthDate: z.coerce.date(),
  gender: z.string(),
  maritalStatus: z.string().nullable(),
  occupation: z.string().nullable(),
  address: JsonValueSchema,
  bloodType: z.string().nullable(),
  height: z.number().nullable(),
  weight: z.number().nullable(),
  allergies: z.string().array(),
  chronicConditions: z.string().array(),
  medications: z.string().array(),
  surgeries: z.string().array(),
  familyHistory: z.string().array(),
  emergencyContact: JsonValueSchema.nullable(),
  healthInsurance: JsonValueSchema.nullable(),
  communicationPreferences: JsonValueSchema.nullable(),
  hasActiveSubscription: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Patient = z.infer<typeof PatientSchema>

/////////////////////////////////////////
// MEDICAL DOCUMENT SCHEMA
/////////////////////////////////////////

export const MedicalDocumentSchema = z.object({
  id: z.string().cuid(),
  patientId: z.string(),
  name: z.string(),
  type: z.string(),
  fileUrl: z.string(),
  uploadedAt: z.coerce.date(),
})

export type MedicalDocument = z.infer<typeof MedicalDocumentSchema>

/////////////////////////////////////////
// SPECIALTY SCHEMA
/////////////////////////////////////////

export const SpecialtySchema = z.object({
  id: z.string().cuid(),
  name: z.string(),
  description: z.string().nullable(),
  searchCount: z.number().int(),
  createdAt: z.coerce.date(),
})

export type Specialty = z.infer<typeof SpecialtySchema>

/////////////////////////////////////////
// DOCTOR HOSPITAL SCHEMA
/////////////////////////////////////////

export const DoctorHospitalSchema = z.object({
  id: z.string().cuid(),
  doctorId: z.string(),
  hospitalId: z.string(),
  departmentId: z.string().nullable(),
  isActive: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type DoctorHospital = z.infer<typeof DoctorHospitalSchema>

/////////////////////////////////////////
// PRE ANESTHETIC FORM SCHEMA
/////////////////////////////////////////

export const PreAnestheticFormSchema = z.object({
  status: DocumentStatusSchema,
  id: z.string().cuid(),
  appointmentId: z.string(),
  hospitalId: z.string(),
  templateId: z.string().nullable(),
  patientData: JsonValueSchema,
  clinicalEvaluation: JsonValueSchema,
  physicalExam: JsonValueSchema,
  labTests: JsonValueSchema,
  conclusion: JsonValueSchema,
  signatureData: JsonValueSchema.nullable(),
  signedAt: z.coerce.date().nullable(),
  signedBy: z.string().nullable(),
  version: z.number().int(),
  history: JsonValueSchema.array(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PreAnestheticForm = z.infer<typeof PreAnestheticFormSchema>

/////////////////////////////////////////
// PRE ANESTHETIC TEMPLATE SCHEMA
/////////////////////////////////////////

export const PreAnestheticTemplateSchema = z.object({
  id: z.string().cuid(),
  hospitalId: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  fields: JsonValueSchema,
  isActive: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PreAnestheticTemplate = z.infer<typeof PreAnestheticTemplateSchema>

/////////////////////////////////////////
// DIGITAL CERTIFICATE SCHEMA
/////////////////////////////////////////

export const DigitalCertificateSchema = z.object({
  id: z.string().cuid(),
  doctorId: z.string(),
  type: z.string(),
  metadata: JsonValueSchema,
  validUntil: z.coerce.date(),
  isActive: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type DigitalCertificate = z.infer<typeof DigitalCertificateSchema>

/////////////////////////////////////////
// APPOINTMENT SCHEMA
/////////////////////////////////////////

export const AppointmentSchema = z.object({
  consultType: ConsultTypeSchema,
  status: AppointmentStatusSchema,
  appointmentType: AppointmentTypeSchema,
  urgencyLevel: UrgencyLevelSchema.nullable(),
  paymentStatus: PaymentStatusSchema,
  id: z.string().cuid(),
  doctorId: z.string().nullable(),
  patientId: z.string(),
  hospitalId: z.string().nullable(),
  scheduledAt: z.coerce.date(),
  duration: z.number().int(),
  symptoms: z.string().nullable(),
  preConsultForm: JsonValueSchema.nullable(),
  isOnDuty: z.boolean(),
  queuePosition: z.number().int().nullable(),
  acceptedAt: z.coerce.date().nullable(),
  acceptedByDoctorId: z.string().nullable(),
  roomId: z.string().nullable(),
  chatEnabled: z.boolean(),
  recordingEnabled: z.boolean(),
  amount: z.instanceof(Prisma.Decimal, { message: "Field 'amount' must be a Decimal. Location: ['Models', 'Appointment']"}),
  paymentId: z.string().nullable(),
  transactionId: z.string().nullable(),
  isReturn: z.boolean(),
  previousAppointmentId: z.string().nullable(),
  partnerSource: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Appointment = z.infer<typeof AppointmentSchema>

/////////////////////////////////////////
// DOCTOR SCHEDULE SCHEMA
/////////////////////////////////////////

export const DoctorScheduleSchema = z.object({
  id: z.string().cuid(),
  doctorId: z.string(),
  weekDay: z.number().int(),
  startTime: z.string(),
  endTime: z.string(),
  isEnabled: z.boolean(),
  isBreak: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type DoctorSchedule = z.infer<typeof DoctorScheduleSchema>

/////////////////////////////////////////
// SCHEDULE BLOCK SCHEMA
/////////////////////////////////////////

export const ScheduleBlockSchema = z.object({
  type: BlockTypeSchema,
  id: z.string().cuid(),
  doctorId: z.string(),
  startTime: z.coerce.date(),
  endTime: z.coerce.date(),
  reason: z.string().nullable(),
  description: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type ScheduleBlock = z.infer<typeof ScheduleBlockSchema>

/////////////////////////////////////////
// TIME SLOT SCHEMA
/////////////////////////////////////////

export const TimeSlotSchema = z.object({
  id: z.string().cuid(),
  doctorId: z.string(),
  startTime: z.coerce.date(),
  endTime: z.coerce.date(),
  isAvailable: z.boolean(),
  appointmentId: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type TimeSlot = z.infer<typeof TimeSlotSchema>

/////////////////////////////////////////
// TRANSACTION SCHEMA
/////////////////////////////////////////

export const TransactionSchema = z.object({
  status: PaymentStatusSchema,
  paymentMethod: PaymentMethodSchema,
  id: z.string().cuid(),
  appointmentId: z.string().nullable(),
  patientSubscriptionId: z.string().nullable(),
  amount: z.instanceof(Prisma.Decimal, { message: "Field 'amount' must be a Decimal. Location: ['Models', 'Transaction']"}),
  platformFee: z.instanceof(Prisma.Decimal, { message: "Field 'platformFee' must be a Decimal. Location: ['Models', 'Transaction']"}),
  doctorAmount: z.instanceof(Prisma.Decimal, { message: "Field 'doctorAmount' must be a Decimal. Location: ['Models', 'Transaction']"}),
  asaasId: z.string().nullable(),
  dueDate: z.coerce.date(),
  paidAt: z.coerce.date().nullable(),
  refundedAt: z.coerce.date().nullable(),
  doctorId: z.string().nullable(),
  hospitalId: z.string().nullable(),
  partnerSource: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Transaction = z.infer<typeof TransactionSchema>

/////////////////////////////////////////
// INVOICE SCHEMA
/////////////////////////////////////////

export const InvoiceSchema = z.object({
  id: z.string().cuid(),
  transactionId: z.string(),
  number: z.string(),
  series: z.string(),
  url: z.string(),
  issuedAt: z.coerce.date(),
  metadata: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
})

export type Invoice = z.infer<typeof InvoiceSchema>

/////////////////////////////////////////
// PRESCRIPTION SCHEMA
/////////////////////////////////////////

export const PrescriptionSchema = z.object({
  id: z.string().cuid(),
  appointmentId: z.string(),
  memedId: z.string().nullable(),
  content: JsonValueSchema.nullable(),
  pdfUrl: z.string().nullable(),
  status: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Prescription = z.infer<typeof PrescriptionSchema>

/////////////////////////////////////////
// DOCTOR EVALUATION SCHEMA
/////////////////////////////////////////

export const DoctorEvaluationSchema = z.object({
  id: z.string().cuid(),
  doctorId: z.string(),
  patientId: z.string(),
  appointmentId: z.string(),
  rating: z.number().int(),
  comment: z.string().nullable(),
  createdAt: z.coerce.date(),
})

export type DoctorEvaluation = z.infer<typeof DoctorEvaluationSchema>

/////////////////////////////////////////
// DOCTOR DOCUMENT SCHEMA
/////////////////////////////////////////

export const DoctorDocumentSchema = z.object({
  type: DocumentTypeSchema,
  status: DocumentStatusSchema,
  id: z.string().cuid(),
  doctorId: z.string(),
  fileName: z.string(),
  fileUrl: z.string(),
  notes: z.string().nullable(),
  reviewedAt: z.coerce.date().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type DoctorDocument = z.infer<typeof DoctorDocumentSchema>

/////////////////////////////////////////
// MESSAGE SCHEMA
/////////////////////////////////////////

export const MessageSchema = z.object({
  type: MessageTypeSchema,
  senderRole: UserRoleSchema.nullable(),
  id: z.string().cuid(),
  appointmentId: z.string(),
  senderId: z.string(),
  content: z.string(),
  metadata: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
})

export type Message = z.infer<typeof MessageSchema>

/////////////////////////////////////////
// NOTIFICATION SCHEMA
/////////////////////////////////////////

export const NotificationSchema = z.object({
  type: NotificationTypeSchema,
  id: z.string().cuid(),
  userId: z.string(),
  appointmentId: z.string(),
  title: z.string(),
  message: z.string(),
  read: z.boolean(),
  createdAt: z.coerce.date(),
})

export type Notification = z.infer<typeof NotificationSchema>

/////////////////////////////////////////
// ATTACHMENT SCHEMA
/////////////////////////////////////////

export const AttachmentSchema = z.object({
  id: z.string().cuid(),
  appointmentId: z.string(),
  name: z.string(),
  url: z.string(),
  type: z.string(),
  size: z.number().int(),
  uploadedBy: z.string(),
  createdAt: z.coerce.date(),
})

export type Attachment = z.infer<typeof AttachmentSchema>

/////////////////////////////////////////
// APPOINTMENT NOTE SCHEMA
/////////////////////////////////////////

export const AppointmentNoteSchema = z.object({
  id: z.string().cuid(),
  appointmentId: z.string(),
  doctorId: z.string(),
  content: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type AppointmentNote = z.infer<typeof AppointmentNoteSchema>

/////////////////////////////////////////
// MEDICAL RECORD SCHEMA
/////////////////////////////////////////

export const MedicalRecordSchema = z.object({
  id: z.string().cuid(),
  appointmentId: z.string(),
  main_complaint: z.string().nullable(),
  diagnosis: z.string().nullable(),
  conduct: z.string().nullable(),
  cid_codes: z.string().array(),
  cid_descriptions: z.string().array(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type MedicalRecord = z.infer<typeof MedicalRecordSchema>

/////////////////////////////////////////
// COMMISSION SETTINGS SCHEMA
/////////////////////////////////////////

export const CommissionSettingsSchema = z.object({
  id: z.string().cuid(),
  platformFeeRate: z.instanceof(Prisma.Decimal, { message: "Field 'platformFeeRate' must be a Decimal. Location: ['Models', 'CommissionSettings']"}),
  minFeeAmount: z.instanceof(Prisma.Decimal, { message: "Field 'minFeeAmount' must be a Decimal. Location: ['Models', 'CommissionSettings']"}),
  maxFeeAmount: z.instanceof(Prisma.Decimal, { message: "Field 'maxFeeAmount' must be a Decimal. Location: ['Models', 'CommissionSettings']"}).nullable(),
  isActive: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type CommissionSettings = z.infer<typeof CommissionSettingsSchema>

/////////////////////////////////////////
// PATIENT SUBSCRIPTION SCHEMA
/////////////////////////////////////////

export const PatientSubscriptionSchema = z.object({
  status: PatientSubscriptionStatusSchema,
  cycle: SubscriptionCycleSchema.nullable(),
  lastPaymentStatus: PaymentStatusSchema.nullable(),
  paymentMethod: PaymentMethodSchema.nullable(),
  id: z.string().cuid(),
  patientId: z.string(),
  planId: z.string(),
  planName: z.string(),
  planPrice: z.instanceof(Prisma.Decimal, { message: "Field 'planPrice' must be a Decimal. Location: ['Models', 'PatientSubscription']"}),
  asaasSubscriptionId: z.string().nullable(),
  startDate: z.coerce.date(),
  endDate: z.coerce.date().nullable(),
  nextBillingDate: z.coerce.date().nullable(),
  currentPeriodStart: z.coerce.date().nullable(),
  currentPeriodEnd: z.coerce.date().nullable(),
  gracePeriodDays: z.number().int(),
  retryCount: z.number().int(),
  maxRetryCount: z.number().int(),
  overdueSince: z.coerce.date().nullable(),
  unpaidSince: z.coerce.date().nullable(),
  lastPaymentAt: z.coerce.date().nullable(),
  failedPaymentAt: z.coerce.date().nullable(),
  cancelAtPeriodEnd: z.boolean(),
  pausedAt: z.coerce.date().nullable(),
  canceledAt: z.coerce.date().nullable(),
  renewalCount: z.number().int(),
  externalCustomerId: z.string().nullable(),
  metadata: JsonValueSchema.nullable(),
  consultationsIncluded: z.number().int(),
  consultationsUsed: z.number().int(),
  lastResetDate: z.coerce.date(),
  autoRenewal: z.boolean(),
  consultationsUsedThisPeriod: z.number().int(),
  periodStartDate: z.coerce.date(),
  periodEndDate: z.coerce.date().nullable(),
  lastNotificationAt: z.coerce.date().nullable(),
  notificationCount: z.number().int(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PatientSubscription = z.infer<typeof PatientSubscriptionSchema>

/////////////////////////////////////////
// CONSULTATION USAGE SCHEMA
/////////////////////////////////////////

export const ConsultationUsageSchema = z.object({
  type: ConsultationUsageTypeSchema,
  id: z.string().cuid(),
  patientId: z.string(),
  subscriptionId: z.string().nullable(),
  appointmentId: z.string(),
  usedAt: z.coerce.date(),
  doctorId: z.string().nullable(),
  amount: z.instanceof(Prisma.Decimal, { message: "Field 'amount' must be a Decimal. Location: ['Models', 'ConsultationUsage']"}),
  commission: z.instanceof(Prisma.Decimal, { message: "Field 'commission' must be a Decimal. Location: ['Models', 'ConsultationUsage']"}),
  createdAt: z.coerce.date(),
})

export type ConsultationUsage = z.infer<typeof ConsultationUsageSchema>

/////////////////////////////////////////
// SUBSCRIPTION NOTIFICATION SCHEMA
/////////////////////////////////////////

export const SubscriptionNotificationSchema = z.object({
  id: z.string().cuid(),
  subscriptionId: z.string(),
  type: z.string(),
  title: z.string(),
  message: z.string(),
  sentAt: z.coerce.date(),
  readAt: z.coerce.date().nullable(),
})

export type SubscriptionNotification = z.infer<typeof SubscriptionNotificationSchema>

/////////////////////////////////////////
// DOCTOR WITHDRAWAL SCHEMA
/////////////////////////////////////////

export const DoctorWithdrawalSchema = z.object({
  id: z.string().cuid(),
  doctorId: z.string(),
  amount: z.instanceof(Prisma.Decimal, { message: "Field 'amount' must be a Decimal. Location: ['Models', 'DoctorWithdrawal']"}),
  status: z.string(),
  requestedAt: z.coerce.date(),
  processedAt: z.coerce.date().nullable(),
  bankAccount: JsonValueSchema,
  notes: z.string().nullable(),
})

export type DoctorWithdrawal = z.infer<typeof DoctorWithdrawalSchema>

/////////////////////////////////////////
// SECURE PAYMENT DATA SCHEMA
/////////////////////////////////////////

export const SecurePaymentDataSchema = z.object({
  id: z.string().cuid(),
  transactionId: z.string(),
  paymentToken: z.string(),
  tokenIv: z.string(),
  tokenTag: z.string(),
  paymentHash: z.string(),
  lastFourDigits: z.string(),
  cardBrand: z.string().nullable(),
  cardType: z.string().nullable(),
  encryptionVersion: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type SecurePaymentData = z.infer<typeof SecurePaymentDataSchema>
