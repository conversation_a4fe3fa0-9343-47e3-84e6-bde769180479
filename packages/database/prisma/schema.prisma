datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  directUrl    = env("DIRECT_URL")
  relationMode = "prisma"
}

generator client {
  provider = "prisma-client-js"
}

generator zod {
  provider         = "zod-prisma-types"
  output           = "../src/zod"
  createInputTypes = false
  addIncludeType   = false
  addSelectType    = false
}

// =======================================
// PARTE 1: Supastarter Original (Intacto)
// =======================================

enum UserRole {
  USER
  ADMIN
  DOCTOR
  PATIENT
  SECRETARY
  HOSPITAL
}

model User {
  id                 String                  @id @default(cuid())
  email              String                  @unique
  emailVerified      Boolean                 @default(false)
  role               UserRole                @default(PATIENT)
  name               String?
  avatarUrl          String?
  createdAt          DateTime                @default(now())
  hashedPassword     String?
  onboardingComplete Boolean                 @default(false)
  oauthAccounts      UserOauthAccount[]
  sessions           UserSession[]
  memberships        TeamMembership[]
  verificationtokens UserVerificationToken[]
  oneTimePasswords   UserOneTimePassword[]
  gatewayCustomerId  String? // ID do cliente no gateway de pagamento (Asaas)

  // Relações com ZapVida
  doctor        Doctor?
  patient       Patient?
  messages      Message[]
  notifications Notification[]
  attachments   Attachment[]
  phone         String? // Adicionado para ZapVida
}

model UserSession {
  id             String   @id
  userId         String
  expiresAt      DateTime
  impersonatorId String?
  user           User     @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([userId])
}

model UserOauthAccount {
  id             String @id @default(cuid())
  providerId     String
  providerUserId String
  userId         String
  user           User   @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@unique([providerId, providerUserId])
}

model UserVerificationToken {
  id      String   @id @default(cuid())
  userId  String
  user    User     @relation(references: [id], fields: [userId], onDelete: Cascade)
  expires DateTime

  @@index([userId])
}

enum UserOneTimePasswordType {
  SIGNUP
  LOGIN
  PASSWORD_RESET
}

model UserOneTimePassword {
  id         String                  @id @default(cuid())
  userId     String
  user       User                    @relation(references: [id], fields: [userId], onDelete: Cascade)
  code       String
  type       UserOneTimePasswordType
  identifier String
  expires    DateTime
}

model Team {
  id           String           @id @default(cuid())
  name         String
  avatarUrl    String?
  memberships  TeamMembership[]
  subscription Subscription?
  invitations  TeamInvitation[]

  teamType String? // "HOSPITAL", "DEPARTMENT", "SPECIALTY"
  metadata Json? // Dados específicos sobre o time (hospital, departamento, etc.)
  hospital Hospital? // Se for um hospital ou equipe hospitalar
}

enum TeamMemberRole {
  MEMBER
  OWNER
  DOCTOR
  SECRETARY
  ADMIN
}

model TeamMembership {
  id        String         @id @default(cuid())
  team      Team           @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId    String
  user      User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  role      TeamMemberRole @default(MEMBER)
  isCreator Boolean        @default(false)

  @@unique([teamId, userId])
  @@index([userId])
}

model TeamInvitation {
  id        String         @id @default(cuid())
  team      Team           @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId    String
  email     String
  role      TeamMemberRole @default(MEMBER)
  createdAt DateTime       @default(now())
  expiresAt DateTime       @updatedAt

  @@unique([teamId, email])
}

enum SubscriptionStatus {
  TRIALING
  ACTIVE
  PAUSED
  CANCELED
  PAST_DUE
  UNPAID
  INCOMPLETE
  EXPIRED
}

model Subscription {
  id              String             @id
  team            Team               @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId          String             @unique
  customerId      String
  status          SubscriptionStatus
  planId          String
  variantId       String
  nextPaymentDate DateTime?
}

// Enums
enum DoctorStatus {
  PENDING
  APPROVED
  REJECTED
}

enum OnlineStatus {
  ONLINE
  OFFLINE
  BUSY
}

enum ConsultType {
  VIDEO
  AUDIO
  CHAT
}

enum AppointmentStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELED
  NO_SHOW
  WAITING_ON_DUTY
  ACCEPTED_BY_DOCTOR
}

enum PaymentStatus {
  PENDING
  PAID
  REFUNDED
  FAILED
}

enum PaymentMethod {
  CREDIT_CARD
  BOLETO
  PIX
}

enum NotificationType {
  APPOINTMENT_CREATED
  APPOINTMENT_REMINDER
  APPOINTMENT_CANCELED
  PRESCRIPTION_READY
  DOCTOR_APPROVED
  PAYMENT_CONFIRMED
}

enum MessageType {
  TEXT
  AUDIO
  FILE
  SYSTEM
}

enum BlockType {
  VACATION
  HOLIDAY
  LUNCH
  PERSONAL
  OTHER
}

enum DocumentStatus {
  PENDING
  APPROVED
  REJECTED
}

enum DocumentType {
  CRM
  SPECIALTY
  ID
  COLLEGE_DEGREE
  RESIDENCY
  OTHER
}

enum AppointmentType {
  TELEMEDICINE
  AMBULATORY
  PRE_ANESTHETIC
}

// Novo enum para níveis de urgência do plantão
enum UrgencyLevel {
  HIGH
  MEDIUM
  LOW
}

// Modelos ZapVida

model Hospital {
  id           String  @id @default(cuid())
  teamId       String  @unique // Relacionamento com Team
  team         Team    @relation(fields: [teamId], references: [id])
  name         String
  cnpj         String  @unique
  contactEmail String
  contactPhone String
  address      Json
  logoUrl      String?
  settings     Json // Configurações específicas do hospital

  // Relationships
  doctors            DoctorHospital[]
  departments        Department[]
  appointments       Appointment[]
  preAnestheticForms PreAnestheticForm[]
  templates          PreAnestheticTemplate[]
  transactions       Transaction[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("hospitals")
}

model Department {
  id          String           @id @default(cuid())
  hospitalId  String
  hospital    Hospital         @relation(fields: [hospitalId], references: [id])
  teamId      String? // Opcional: pode ser sua própria Team
  name        String
  description String?
  isActive    Boolean          @default(true)
  doctors     DoctorHospital[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([hospitalId])
  @@map("departments")
}

model Doctor {
  id                   String       @id @default(cuid())
  userId               String       @unique
  user                 User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  crm                  String
  crmState             String
  biography            String?      @db.Text
  consultationPrice    Decimal      @db.Decimal(10, 2)
  consultationDuration Int          @default(30)
  returnPeriod         Int          @default(0)
  memedToken           String?
  documentStatus       DoctorStatus @default(PENDING)
  onlineStatus         OnlineStatus @default(OFFLINE)
  rating               Float?
  totalRatings         Int          @default(0)

  // Configurações específicas de plantão
  isAvailableForOnDuty  Boolean  @default(false) // Se está disponível para plantão
  onDutyPriceMultiplier Decimal? @db.Decimal(3, 2) // Multiplicador de preço para plantão
  maxConcurrentOnDuty   Int      @default(3) // Máximo de atendimentos simultâneos em plantão

  scheduleConfig  Json?
  consultTypes    ConsultType[]
  customQuestions Json?
  bankAccount     Json?
  asaasId         String?

  specialties          Specialty[]
  appointments         Appointment[]
  acceptedAppointments Appointment[]        @relation("AcceptedAppointments") // Appointments aceitos pelo médico
  doctorSchedules      DoctorSchedule[]
  scheduleBlocks       ScheduleBlock[]
  timeSlots            TimeSlot[]
  evaluations          DoctorEvaluation[]
  documents            DoctorDocument[]
  digitalCertificates  DigitalCertificate[]
  hospitals            DoctorHospital[]
  transactions         Transaction[]

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  appointmentNotes  AppointmentNote[]
  ConsultationUsage ConsultationUsage[]
  DoctorWithdrawal  DoctorWithdrawal[]

  @@unique([crm, crmState])
  @@index([documentStatus, onlineStatus])
  @@index([onlineStatus])
  @@index([rating])
  @@map("doctors")
}

model Patient {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Informações Pessoais
  cpf           String   @unique
  birthDate     DateTime @db.Timestamptz
  gender        String
  maritalStatus String?
  occupation    String?

  // Endereço
  address Json

  // Informações de Saúde
  bloodType         String?
  height            Float?
  weight            Float?
  allergies         String[]
  chronicConditions String[]
  medications       String[]
  surgeries         String[]
  familyHistory     String[]

  emergencyContact Json?

  healthInsurance Json?

  // Preferências
  communicationPreferences Json? // email, sms, whatsapp

  // Relações
  appointments     Appointment[]
  evaluations      DoctorEvaluation[]
  medicalDocuments MedicalDocument[]

  // Novos campos para assinaturas
  subscriptions         PatientSubscription[]
  consultationUsages    ConsultationUsage[]
  hasActiveSubscription Boolean               @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("patients")
}

model MedicalDocument {
  id         String   @id @default(cuid())
  patientId  String
  patient    Patient  @relation(fields: [patientId], references: [id])
  name       String
  type       String
  fileUrl    String
  uploadedAt DateTime @default(now())

  @@index([patientId])
  @@map("medical_documents")
}

model Specialty {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  searchCount Int      @default(0)
  doctors     Doctor[]
  createdAt   DateTime @default(now())

  @@map("specialties")
}

model DoctorHospital {
  id           String      @id @default(cuid())
  doctorId     String
  hospitalId   String
  departmentId String?
  doctor       Doctor      @relation(fields: [doctorId], references: [id])
  hospital     Hospital    @relation(fields: [hospitalId], references: [id])
  department   Department? @relation(fields: [departmentId], references: [id])
  isActive     Boolean     @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([doctorId, hospitalId])
  @@index([doctorId])
  @@index([hospitalId])
  @@index([departmentId])
  @@map("doctor_hospitals")
}

model PreAnestheticForm {
  id            String                 @id @default(cuid())
  appointmentId String                 @unique
  appointment   Appointment            @relation(fields: [appointmentId], references: [id])
  hospitalId    String
  hospital      Hospital               @relation(fields: [hospitalId], references: [id])
  templateId    String?
  template      PreAnestheticTemplate? @relation(fields: [templateId], references: [id])

  // Patient data (copied from appointment for history)
  patientData Json

  // Form fields
  clinicalEvaluation Json
  physicalExam       Json
  labTests           Json
  conclusion         Json

  // Status and signature
  status        DocumentStatus @default(PENDING)
  signatureData Json?
  signedAt      DateTime?      @db.Timestamptz
  signedBy      String?

  // Audit trail
  version Int    @default(1)
  history Json[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([hospitalId])
  @@index([templateId])
  @@map("pre_anesthetic_forms")
}

model PreAnestheticTemplate {
  id          String              @id @default(cuid())
  hospitalId  String
  hospital    Hospital            @relation(fields: [hospitalId], references: [id])
  name        String
  description String?
  fields      Json // Custom template fields
  isActive    Boolean             @default(true)
  forms       PreAnestheticForm[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([hospitalId])
  @@map("pre_anesthetic_templates")
}

model DigitalCertificate {
  id         String   @id @default(cuid())
  doctorId   String
  doctor     Doctor   @relation(fields: [doctorId], references: [id])
  type       String // VIDAAS_A3, A1
  metadata   Json // Certificate specific data
  validUntil DateTime @db.Timestamptz
  isActive   Boolean  @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([doctorId])
  @@map("digital_certificates")
}

model Appointment {
  id         String    @id @default(cuid())
  doctorId   String?
  doctor     Doctor?   @relation(fields: [doctorId], references: [id])
  patientId  String
  patient    Patient   @relation(fields: [patientId], references: [id])
  hospitalId String?
  hospital   Hospital? @relation(fields: [hospitalId], references: [id])

  scheduledAt     DateTime          @db.Timestamptz
  consultType     ConsultType
  duration        Int
  status          AppointmentStatus @default(SCHEDULED)
  symptoms        String?           @db.Text
  preConsultForm  Json?
  appointmentType AppointmentType   @default(TELEMEDICINE)

  // Campos específicos para plantão
  isOnDuty           Boolean       @default(false) // Indica se é consulta de plantão
  urgencyLevel       UrgencyLevel? // Nível de urgência (HIGH, MEDIUM, LOW)
  queuePosition      Int? // Posição na fila (calculada dinamicamente)
  acceptedAt         DateTime?     @db.Timestamptz // Quando o médico aceitou o atendimento
  acceptedByDoctorId String? // ID do médico que aceitou (pode ser diferente do doctorId inicial)
  acceptedByDoctor   Doctor?       @relation("AcceptedAppointments", fields: [acceptedByDoctorId], references: [id])

  roomId           String?
  chatEnabled      Boolean @default(true)
  recordingEnabled Boolean @default(false)

  amount                Decimal       @db.Decimal(10, 2)
  paymentStatus         PaymentStatus @default(PENDING)
  paymentId             String?
  transactionId         String?
  isReturn              Boolean       @default(false)
  previousAppointmentId String?       @unique
  partnerSource         String? // Origem da consulta (farmacia, loopmais, etc.)

  evaluation        DoctorEvaluation?
  prescription      Prescription?
  timeSlot          TimeSlot?
  transaction       Transaction?
  preAnestheticForm PreAnestheticForm?

  notifications Notification[]
  messages      Message[]
  attachments   Attachment[]

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  appointmentNotes  AppointmentNote[]
  medicalRecord     MedicalRecord?
  ConsultationUsage ConsultationUsage[]

  @@index([doctorId])
  @@index([patientId])
  @@index([hospitalId])
  @@index([acceptedByDoctorId])
  // Índices otimizados para fila de plantão
  @@index([isOnDuty, paymentStatus, status, acceptedByDoctorId, createdAt], name: "idx_plantao_queue")
  @@index([isOnDuty, acceptedByDoctorId, status], name: "idx_plantao_doctor")
  @@index([doctorId, status, isOnDuty], name: "idx_doctor_plantao")
  @@index([status, isOnDuty, urgencyLevel], name: "idx_plantao_urgency")
  @@map("appointments")
}

model DoctorSchedule {
  id        String   @id @default(cuid())
  doctorId  String
  doctor    Doctor   @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  weekDay   Int // 0 = Domingo, 1 = Segunda, ..., 6 = Sábado
  startTime String // Formato HH:MM (e.g., "09:00")
  endTime   String // Formato HH:MM (e.g., "18:00")
  isEnabled Boolean  @default(true) // Permite desativar um horário sem removê-lo
  isBreak   Boolean  @default(false) // Indica se é um intervalo (ex: almoço)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([doctorId, weekDay, startTime, endTime]) // Garante que não haja horários duplicados
  @@index([doctorId])
}

model ScheduleBlock {
  id          String    @id @default(cuid())
  doctorId    String
  doctor      Doctor    @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  startTime   DateTime // Data e hora de início do bloqueio
  endTime     DateTime // Data e hora de fim do bloqueio
  reason      String? // Motivo opcional (ex: Férias, Conferência)
  type        BlockType // Tipo de bloqueio (VACATION, HOLIDAY, LUNCH, PERSONAL, OTHER)
  description String? // Descrição adicional opcional
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([doctorId, startTime, endTime])
}

model TimeSlot {
  id          String   @id @default(cuid())
  doctorId    String
  doctor      Doctor   @relation(fields: [doctorId], references: [id])
  startTime   DateTime @db.Timestamptz
  endTime     DateTime @db.Timestamptz
  isAvailable Boolean  @default(true)

  appointmentId String?      @unique
  appointment   Appointment? @relation(fields: [appointmentId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([doctorId])
  @@map("time_slots")
}

model Transaction {
  id                    String               @id @default(cuid())
  appointmentId         String?              @unique
  appointment           Appointment?         @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  patientSubscriptionId String?
  patientSubscription   PatientSubscription? @relation(fields: [patientSubscriptionId], references: [id])
  amount                Decimal              @db.Decimal(10, 2)
  platformFee           Decimal              @db.Decimal(10, 2)
  doctorAmount          Decimal              @db.Decimal(10, 2)
  status                PaymentStatus
  paymentMethod         PaymentMethod
  asaasId               String?
  dueDate               DateTime             @db.Timestamptz
  paidAt                DateTime?            @db.Timestamptz
  refundedAt            DateTime?            @db.Timestamptz
  invoice               Invoice?
  doctorId              String?
  doctor                Doctor?              @relation(fields: [doctorId], references: [id])
  hospitalId            String?              @db.Uuid
  hospital              Hospital?            @relation(fields: [hospitalId], references: [id])
  partnerSource         String? // Novo campo para rastrear a origem do pagamento

  // Relacionamento com dados de cartão criptografados
  securePaymentData SecurePaymentData?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([doctorId])
  @@index([hospitalId])
  @@index([patientSubscriptionId])
  @@map("transactions")
}

model Invoice {
  id            String      @id @default(cuid())
  transactionId String      @unique
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  number        String      @unique
  series        String
  url           String
  issuedAt      DateTime    @db.Timestamptz
  metadata      Json?

  createdAt DateTime @default(now())

  @@map("invoices")
}

model Prescription {
  id            String      @id @default(cuid())
  appointmentId String      @unique
  appointment   Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  memedId       String? // ID from Memed platform
  content       Json? // Store the prescription content from Memed
  pdfUrl        String? // URL to the prescription PDF
  status        String      @default("active") // active, cancelled, expired

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("prescriptions")
}

model DoctorEvaluation {
  id            String      @id @default(cuid())
  doctorId      String
  doctor        Doctor      @relation(fields: [doctorId], references: [id])
  patientId     String
  patient       Patient     @relation(fields: [patientId], references: [id])
  appointmentId String      @unique
  appointment   Appointment @relation(fields: [appointmentId], references: [id])
  rating        Int
  comment       String?     @db.Text

  createdAt DateTime @default(now())

  @@index([doctorId])
  @@index([patientId])
  @@map("doctor_evaluations")
}

model DoctorDocument {
  id         String         @id @default(cuid())
  doctorId   String
  doctor     Doctor         @relation(fields: [doctorId], references: [id])
  type       DocumentType
  fileName   String
  fileUrl    String
  status     DocumentStatus @default(PENDING)
  notes      String?        @db.Text
  reviewedAt DateTime?      @db.Timestamptz

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([doctorId])
  @@map("doctor_documents")
}

model Message {
  id            String      @id @default(cuid())
  appointmentId String
  appointment   Appointment @relation(fields: [appointmentId], references: [id])
  senderId      String
  sender        User        @relation(fields: [senderId], references: [id])
  type          MessageType @default(TEXT)
  content       String      @db.Text
  metadata      Json?
  senderRole    UserRole? // Add role of the message sender (DOCTOR, PATIENT, etc.)

  createdAt DateTime @default(now())

  @@index([appointmentId])
  @@index([senderId])
  @@map("messages")
}

model Notification {
  id            String           @id @default(cuid())
  userId        String
  user          User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  appointmentId String
  appointment   Appointment      @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  type          NotificationType
  title         String
  message       String
  read          Boolean          @default(false)

  createdAt DateTime @default(now())

  @@index([userId])
  @@index([appointmentId])
  @@map("notifications")
}

model Attachment {
  id            String      @id @default(cuid())
  appointmentId String
  appointment   Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  name          String
  url           String
  type          String
  size          Int
  uploadedBy    String
  uploader      User        @relation(fields: [uploadedBy], references: [id])

  createdAt DateTime @default(now())

  @@index([appointmentId])
  @@index([uploadedBy])
  @@map("attachments")
}

model AppointmentNote {
  id            String      @id @default(cuid())
  appointmentId String
  appointment   Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  doctorId      String
  doctor        Doctor      @relation(fields: [doctorId], references: [id])
  content       String      @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([appointmentId])
  @@index([doctorId])
  @@map("appointment_notes")
}

model MedicalRecord {
  id             String      @id @default(cuid())
  appointmentId  String      @unique
  appointment    Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  main_complaint String?     @db.Text
  diagnosis      String?     @db.Text
  conduct        String?     @db.Text

  // CID - Código Internacional de Doenças (obrigatório por lei)
  cid_codes        String[] // Array de códigos CID (ex: ["A09", "B34.2"])
  cid_descriptions String[] // Descrições dos CIDs para referência

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("medical_records")
}

model CommissionSettings {
  id              String   @id @default(cuid())
  platformFeeRate Decimal  @db.Decimal(5, 2)
  minFeeAmount    Decimal  @db.Decimal(10, 2)
  maxFeeAmount    Decimal? @db.Decimal(10, 2)
  isActive        Boolean  @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("commission_settings")
}

// =======================================
// PARTE 2: Modelos de Assinatura
// =======================================

enum PatientSubscriptionStatus {
  ACTIVE
  CANCELED
  PAST_DUE
  UNPAID
  PAUSED
}

enum ConsultationUsageType {
  SUBSCRIPTION // Consulta via assinatura
  SINGLE_PAYMENT // Consulta avulsa
  ON_DUTY // Consulta de plantão
}

enum SubscriptionCycle {
  WEEKLY
  BIWEEKLY
  MONTHLY
  QUARTERLY
  SEMIANNUALLY
  YEARLY
}

// Plano de assinatura para pacientes
model PatientSubscription {
  id                    String                    @id @default(cuid())
  patientId             String
  patient               Patient                   @relation(fields: [patientId], references: [id], onDelete: Cascade)
  planId                String // ID do plano (ex: "zapvida-sempre")
  planName              String // Nome do plano
  planPrice             Decimal                   @db.Decimal(10, 2)
  asaasSubscriptionId   String? // ID da assinatura no Asaas
  status                PatientSubscriptionStatus @default(ACTIVE)
  startDate             DateTime                  @default(now())
  endDate               DateTime?
  nextBillingDate       DateTime?
  currentPeriodStart    DateTime?
  currentPeriodEnd      DateTime?
  cycle                 SubscriptionCycle?        @default(MONTHLY)
  gracePeriodDays       Int                       @default(3) // Alterado de 0 para 3
  retryCount            Int                       @default(0)
  maxRetryCount         Int                       @default(3)
  overdueSince          DateTime?
  unpaidSince           DateTime?
  lastPaymentStatus     PaymentStatus?
  lastPaymentAt         DateTime?
  failedPaymentAt       DateTime?
  cancelAtPeriodEnd     Boolean                   @default(false)
  pausedAt              DateTime?
  canceledAt            DateTime?
  renewalCount          Int                       @default(0)
  paymentMethod         PaymentMethod?
  externalCustomerId    String?
  metadata              Json?
  consultationsIncluded Int                       @default(2) // Consultas incluídas por mês
  consultationsUsed     Int                       @default(0) // Consultas utilizadas no mês atual
  lastResetDate         DateTime                  @default(now()) // Data do último reset das consultas

  // Novos campos para melhorias
  autoRenewal                 Boolean   @default(true)
  consultationsUsedThisPeriod Int       @default(0)
  periodStartDate             DateTime  @default(now())
  periodEndDate               DateTime?
  lastNotificationAt          DateTime?
  notificationCount           Int       @default(0)

  // Relações
  consultationUsages ConsultationUsage[]
  transactions       Transaction[]
  notifications      SubscriptionNotification[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([patientId, status])
  @@index([patientId])
  @@index([status])
  @@index([nextBillingDate])
  @@map("patient_subscriptions")
}

// Histórico de uso de consultas
model ConsultationUsage {
  id                  String                @id @default(cuid())
  patientId           String
  patient             Patient               @relation(fields: [patientId], references: [id], onDelete: Cascade)
  subscriptionId      String?
  patientSubscription PatientSubscription?  @relation(fields: [subscriptionId], references: [id])
  appointmentId       String
  appointment         Appointment           @relation(fields: [appointmentId], references: [id])
  usedAt              DateTime              @default(now())
  type                ConsultationUsageType @default(SUBSCRIPTION)

  // Novos campos para melhorias
  doctorId   String?
  doctor     Doctor? @relation(fields: [doctorId], references: [id])
  amount     Decimal @default(0) @db.Decimal(10, 2)
  commission Decimal @default(0) @db.Decimal(10, 2)

  createdAt DateTime @default(now())

  @@index([appointmentId])
  @@index([subscriptionId])
  @@index([patientId])
  @@index([doctorId])
  @@index([usedAt])
  @@map("consultation_usages")
}

// Notificações de assinatura
model SubscriptionNotification {
  id             String              @id @default(cuid())
  subscriptionId String
  subscription   PatientSubscription @relation(fields: [subscriptionId], references: [id])
  type           String // PAYMENT_DUE, PAYMENT_FAILED, USAGE_LIMIT, etc
  title          String
  message        String
  sentAt         DateTime            @default(now())
  readAt         DateTime?

  @@index([subscriptionId])
  @@index([sentAt])
  @@map("subscription_notifications")
}

// Saques de médicos
model DoctorWithdrawal {
  id          String    @id @default(cuid())
  doctorId    String
  doctor      Doctor    @relation(fields: [doctorId], references: [id])
  amount      Decimal   @db.Decimal(10, 2)
  status      String    @default("PENDING") // PENDING, APPROVED, REJECTED, COMPLETED
  requestedAt DateTime  @default(now())
  processedAt DateTime?
  bankAccount Json
  notes       String?

  @@index([doctorId])
  @@index([status])
  @@map("doctor_withdrawals")
}

// Modelo seguro para armazenar dados de pagamento criptografados
// Campos disfarçados para não revelar que são dados de cartão
model SecurePaymentData {
  id            String      @id @default(cuid())
  transactionId String      @unique
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  // Campos disfarçados - parecem dados genéricos de pagamento
  paymentToken String // Dados criptografados do cartão completo
  tokenIv      String // IV para criptografia
  tokenTag     String // Tag de autenticação

  // Hash para busca sem descriptografia
  paymentHash String // Hash do número do cartão para busca

  // Campos adicionais para rastreamento
  lastFourDigits String // Últimos 4 dígitos (não criptografados para exibição)
  cardBrand      String? // Bandeira do cartão (Visa, Mastercard, etc.)
  cardType       String? // Tipo do cartão (Crédito, Débito)

  // Metadados de segurança
  encryptionVersion String   @default("v1") // Versão do algoritmo de criptografia
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Índices para performance
  @@index([paymentHash])
  @@index([lastFourDigits])
  @@index([cardBrand])
  @@map("secure_payment_data") // Nome da tabela disfarçado
}
