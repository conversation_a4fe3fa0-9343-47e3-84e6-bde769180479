import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { db } from "database";

// Function to notify patient via WhatsApp when a doctor accepts their appointment
async function notifyPatient(appointmentId: string) {
  try {
    // Dynamic import to avoid circular dependencies
    const { sendPlantaoAcceptedNotification } = await import("../../../../../apps/web/actions/appointments/send-plantao-notifications");
    return await sendPlantaoAcceptedNotification(appointmentId);
  } catch (error) {
    console.error("[ACCEPT_APPOINTMENT] Error notifying patient:", error);
    return { success: false, error };
  }
}

export const acceptAppointment = protectedProcedure
  .input(
    z.object({
      appointmentId: z.string(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    const startTime = Date.now();
    
    try {
      const { user } = ctx;

      console.log("[ACCEPT_APPOINTMENT] Starting acceptance process", {
        appointmentId: input.appointmentId,
        userId: user.id,
        timestamp: new Date().toISOString()
      });

      // Verificar se o usuário é um médico
      const doctor = await db.doctor.findUnique({
        where: { userId: user.id },
        include: {
          user: {
            select: {
              name: true,
              phone: true,
            },
          },
          specialties: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!doctor) {
        console.warn("[ACCEPT_APPOINTMENT] Non-doctor user attempted to accept", {
          userId: user.id
        });
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Apenas médicos podem aceitar atendimentos",
        });
      }

      console.log("[ACCEPT_APPOINTMENT] Doctor validated", {
        doctorId: doctor.id,
        doctorName: doctor.user.name
      });

      // Buscar o appointment com todos os dados necessários
      const appointment = await db.appointment.findUnique({
        where: { id: input.appointmentId },
        include: {
          patient: {
            include: {
              user: true,
            },
          },
        },
      });

      if (!appointment) {
        console.error("[ACCEPT_APPOINTMENT] Appointment not found", {
          appointmentId: input.appointmentId
        });
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Consulta não encontrada",
        });
      }

      console.log("[ACCEPT_APPOINTMENT] Appointment found - pre-validation state", {
        appointmentId: appointment.id,
        currentStatus: appointment.status,
        currentAcceptedBy: appointment.acceptedByDoctorId,
        paymentStatus: appointment.paymentStatus,
        isOnDuty: appointment.isOnDuty
      });

      // Validações básicas
      if (appointment.status !== "SCHEDULED") {
        console.warn("[ACCEPT_APPOINTMENT] Invalid status", {
          currentStatus: appointment.status,
          expectedStatus: "SCHEDULED"
        });
        throw new TRPCError({
          code: "CONFLICT",
          message: "Esta consulta não está disponível para aceitar",
        });
      }

      if (appointment.paymentStatus !== "PAID") {
        console.warn("[ACCEPT_APPOINTMENT] Payment not confirmed", {
          paymentStatus: appointment.paymentStatus
        });
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Pagamento não confirmado para esta consulta",
        });
      }

      // CORREÇÃO CRÍTICA: Verificação adicional se já foi aceito
      if (appointment.acceptedByDoctorId !== null) {
        console.warn("[ACCEPT_APPOINTMENT] Appointment already accepted", {
          acceptedByDoctorId: appointment.acceptedByDoctorId,
          attemptingDoctorId: doctor.id
        });
        throw new TRPCError({
          code: "CONFLICT",
          message: "Este atendimento já foi aceito por outro médico",
        });
      }

      console.log("[ACCEPT_APPOINTMENT] All validations passed, attempting atomic update");

      // CORREÇÃO CRÍTICA: Usar transação com lock otimista
      // Isso garante que apenas UM médico consegue aceitar
      const updateResult = await db.$transaction(async (tx) => {
        // Tentar atualizar APENAS SE ainda estiver disponível
        const result = await tx.appointment.updateMany({
          where: { 
            id: input.appointmentId,
            status: "SCHEDULED",
            acceptedByDoctorId: null,  // CRÍTICO: garantir que ainda está disponível
            paymentStatus: "PAID"
          },
          data: {
            status: "IN_PROGRESS",
            doctorId: doctor.id,
            scheduledAt: new Date(),
            acceptedAt: new Date(),
            acceptedByDoctorId: doctor.id,
          },
        });

        console.log("[ACCEPT_APPOINTMENT] Update result", {
          rowsAffected: result.count,
          success: result.count > 0
        });

        // Se não atualizou nenhuma linha, significa que outro médico aceitou primeiro
        if (result.count === 0) {
          // Verificar o estado atual para mensagem de erro mais específica
          const currentState = await tx.appointment.findUnique({
            where: { id: input.appointmentId },
            select: {
              status: true,
              acceptedByDoctorId: true,
              acceptedByDoctor: {
                select: {
                  user: {
                    select: { name: true }
                  }
                }
              }
            }
          });

          console.warn("[ACCEPT_APPOINTMENT] Race condition detected", {
            appointmentId: input.appointmentId,
            attemptingDoctorId: doctor.id,
            currentState
          });

          if (currentState?.acceptedByDoctorId) {
            const otherDoctorName = currentState.acceptedByDoctor?.user?.name || "outro médico";
            throw new TRPCError({
              code: "CONFLICT",
              message: `Este atendimento já foi aceito por ${otherDoctorName}`,
            });
          } else {
            throw new TRPCError({
              code: "CONFLICT",
              message: "Esta consulta não está mais disponível",
            });
          }
        }

        // Buscar o appointment atualizado com todos os dados
        const updatedAppointment = await tx.appointment.findUnique({
          where: { id: input.appointmentId },
          include: {
            patient: {
              include: {
                user: true,
              },
            },
            doctor: {
              include: {
                user: true,
              },
            },
            acceptedByDoctor: {
              include: {
                user: true,
              },
            },
          },
        });

        if (!updatedAppointment) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Erro ao buscar consulta atualizada",
          });
        }

        // Criar notificação para o paciente dentro da transação
        await tx.notification.create({
          data: {
            userId: appointment.patient.userId,
            appointmentId: appointment.id,
            type: "APPOINTMENT_CREATED",
            title: "Médico aceitou seu atendimento",
            message: `O Dr. ${doctor.user.name || 'Médico'} aceitou seu atendimento de plantão. A consulta será iniciada em breve.`,
          },
        });

        console.log("[ACCEPT_APPOINTMENT] Transaction completed successfully", {
          appointmentId: updatedAppointment.id,
          doctorId: doctor.id,
          patientId: updatedAppointment.patient.id
        });

        return updatedAppointment;
      });

      const transactionTime = Date.now() - startTime;
      console.log("[ACCEPT_APPOINTMENT] Atomic update successful", {
        appointmentId: input.appointmentId,
        doctorId: doctor.id,
        transactionTimeMs: transactionTime
      });

      // Enviar notificações assíncronas (não bloquear a resposta)
      // Isso roda em background e não afeta o fluxo principal
      setImmediate(async () => {
        try {
          console.log("[ACCEPT_APPOINTMENT] Sending WhatsApp notification to patient");

          const { sendPlantaoAcceptedImprovedMessage } = await import(
            "../../../../../apps/web/lib/plantao-whatsapp-messages"
          );

          if (updateResult.patient.user.phone) {
            const doctorSpecialty = doctor.specialties?.map((s: { name: string }) => s.name).join(', ') || undefined;

            const whatsappResult = await sendPlantaoAcceptedImprovedMessage({
              appointmentId: updateResult.id,
              patientName: updateResult.patient.user.name || "Paciente",
              patientPhone: updateResult.patient.user.phone,
              doctorName: doctor.user.name || "Médico",
              doctorSpecialty
            });

            console.log("[ACCEPT_APPOINTMENT] WhatsApp notification result", {
              success: whatsappResult.success,
              messagesTotal: whatsappResult.messagesTotal,
              messagesSuccess: whatsappResult.messagesSuccess
            });
          } else {
            console.warn("[ACCEPT_APPOINTMENT] Patient has no phone number");
          }

          // Email backup
          const { sendAppointmentNotifications } = await import(
            "../../../../../apps/web/actions/checkout/notifications/send-notifications"
          );

          await sendAppointmentNotifications(
            {
              id: updateResult.id,
              scheduledAt: updateResult.scheduledAt,
              type: (updateResult as any)?.appointmentType,
              isOnDuty: updateResult.isOnDuty,
            },
            {
              id: updateResult.patient.id,
              user: {
                id: updateResult.patient.user.id,
                name: updateResult.patient.user.name || "Paciente",
                email: updateResult.patient.user.email || "",
                phone: updateResult.patient.user.phone || undefined,
              },
              isNewUser: false,
            },
            {
              id: doctor.id,
              user: {
                id: doctor.userId,
                name: doctor.user.name || "Médico",
                email: updateResult.doctor?.user?.email || user.email || "",
                phone: updateResult.doctor?.user?.phone || undefined,
              },
            },
            { sendEmail: true, sendWhatsApp: false, useDirectLinks: true }
          );

          console.log("[ACCEPT_APPOINTMENT] All notifications sent successfully");
        } catch (notificationError) {
          console.error("[ACCEPT_APPOINTMENT] Error sending notifications (non-blocking):", notificationError);
          // Não falhar a operação por erro de notificação
        }
      });

      const totalTime = Date.now() - startTime;
      console.log("[ACCEPT_APPOINTMENT] Process completed", {
        appointmentId: input.appointmentId,
        totalTimeMs: totalTime,
        success: true
      });

      return updateResult;
    } catch (error) {
      const errorTime = Date.now() - startTime;
      
      if (error instanceof TRPCError) {
        console.error("[ACCEPT_APPOINTMENT] Business logic error", {
          code: error.code,
          message: error.message,
          timeMs: errorTime
        });
        throw error;
      }
      
      console.error("[ACCEPT_APPOINTMENT] Unexpected error", {
        error,
        stack: error instanceof Error ? error.stack : undefined,
        timeMs: errorTime
      });
      
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao aceitar atendimento",
      });
    }
  });

