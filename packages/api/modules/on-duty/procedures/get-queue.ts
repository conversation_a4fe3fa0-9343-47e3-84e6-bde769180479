import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";
import { db } from "database";

export const getQueue = publicProcedure
  .input(
    z.object({
      doctorId: z.string().optional(),
      urgencyLevel: z.enum(["HIGH", "MEDIUM", "LOW"]).optional(),
      status: z.enum(["SCHEDULED", "IN_PROGRESS"]).optional(),
      showOnlyUnassigned: z.boolean().optional().default(true), // New parameter to control filtering
      limit: z.number().min(1).max(50).optional().default(10), // Limitar resultados iniciais
      cursor: z.string().optional(), // Para paginação cursor-based
    })
  )
  .query(async ({ input }) => {
    try {
      // Buscar appointments que são de plantão usando o campo isOnDuty
      const where: any = {
        paymentStatus: "PAID",
        appointmentType: "TELEMEDICINE",
        isOnDuty: true, // Use the proper isOnDuty field instead of symptoms
      };

      if (input.status) {
        where.status = input.status;
      } else {
        where.status = "SCHEDULED";
      }

      // Se um médico específico for fornecido, filtrar por ele (para fila pessoal do médico)
      if (input.doctorId) {
        where.acceptedByDoctorId = input.doctorId; // Show only patients accepted by this doctor
      } else if (input.showOnlyUnassigned) {
        // Se showOnlyUnassigned for true, mostrar apenas pacientes sem médico atribuído
        where.acceptedByDoctorId = null; // Only show unassigned patients
      }

      // Configurar paginação com cursor se fornecido
      const cursorConfig = input.cursor
        ? {
            skip: 1, // Pular o cursor
            cursor: {
              id: input.cursor,
            },
          }
        : {};

      const appointments = await db.appointment.findMany({
        where,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  name: true,
                  avatarUrl: true,
                  // email removido - não usado na lista
                },
              },
            },
          },
          // doctor e acceptedByDoctor removidos da query inicial para performance
          // Podem ser carregados sob demanda quando necessário
        },
        orderBy: [
          {
            createdAt: "asc",
          },
        ],
        take: input.limit + 1, // Pegar um a mais para saber se tem próxima página
        ...cursorConfig,
      });

      // Verificar se tem próxima página
      let hasNextPage = false;
      let appointmentsToProcess = appointments;
      
      if (appointments.length > input.limit) {
        hasNextPage = true;
        appointmentsToProcess = appointments.slice(0, input.limit);
      }

      // Processar e enriquecer os dados dos appointments
      const queueWithMetadata = appointmentsToProcess.map((appointment, index) => {
        const waitTimeMinutes = Math.floor(
          (Date.now() - appointment.createdAt.getTime()) / (1000 * 60)
        );

        // Usar o campo urgencyLevel diretamente do banco de dados
        const urgencyLevel = appointment.urgencyLevel || "LOW";

        // Filtrar por nível de urgência se especificado
        if (input.urgencyLevel && urgencyLevel !== input.urgencyLevel) {
          return null;
        }

        return {
          id: appointment.id,
          doctorId: appointment.doctorId,
          patientId: appointment.patientId,
          scheduledAt: appointment.scheduledAt,
          consultType: appointment.consultType,
          duration: appointment.duration,
          status: appointment.status,
          appointmentType: appointment.appointmentType,
          amount: appointment.amount,
          paymentStatus: appointment.paymentStatus,
          symptoms: appointment.symptoms,
          createdAt: appointment.createdAt,
          updatedAt: appointment.updatedAt,
          patient: appointment.patient,
          queuePosition: index + 1,
          waitTimeMinutes,
          urgencyLevel,
          // Campos específicos para plantão
          isOnDuty: appointment.isOnDuty,
          acceptedAt: appointment.acceptedAt,
          acceptedByDoctorId: appointment.acceptedByDoctorId,
        };
      }).filter((item): item is NonNullable<typeof item> => item !== null); // Remove itens null com type guard

      // Reordenar por prioridade de urgência
      const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };
      queueWithMetadata.sort((a, b) => {
        const priorityDiff = priorityOrder[b.urgencyLevel as keyof typeof priorityOrder] -
                           priorityOrder[a.urgencyLevel as keyof typeof priorityOrder];

        if (priorityDiff !== 0) return priorityDiff;

        // Se mesma prioridade, ordenar por tempo de espera (mais antigo primeiro)
        return a.waitTimeMinutes - b.waitTimeMinutes;
      });

      // Atualizar posições na fila após reordenação
      queueWithMetadata.forEach((item, index) => {
        item.queuePosition = index + 1;
      });

      // Retornar dados com informações de paginação
      return {
        items: queueWithMetadata,
        hasNextPage,
        nextCursor: hasNextPage && queueWithMetadata.length > 0 
          ? queueWithMetadata[queueWithMetadata.length - 1].id 
          : null,
      };
    } catch (error) {
      console.error("[ON_DUTY_QUEUE_ERROR]", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar fila de plantão",
      });
    }
  });
